import React from 'react';
import { useTranslation } from 'react-i18next';
import OCBBankAccountForm from '../components/forms/OCBBankAccountForm';
import { OCBBankAccountFormValues } from '../types/banking.types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang liên kết tài khoản ngân hàng OCB
 */
const OCBBankingPage: React.FC = () => {
  const { t } = useTranslation(['integrations', 'common']);

  // Xử lý submit form
  const handleSubmit = async (values: OCBBankAccountFormValues) => {
    try {
      console.log('OCB Bank Account Data:', values);

      // TODO: Gọi API để lưu thông tin tài khoản OCB
      // await createOCBBankAccount(values);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('integrations:banking.ocb.saveSuccess', 'Đã lưu thông tin tài khoản OCB thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Save OCB account error:', error);
      NotificationUtil.error({
        message: t('integrations:banking.ocb.saveError', 'Có lỗi xảy ra khi lưu thông tin tài khoản OCB'),
        duration: 5000,
      });
      throw error; // Re-throw để form xử lý
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <OCBBankAccountForm onSubmit={handleSubmit} />
    </div>
  );
};

export default OCBBankingPage;
