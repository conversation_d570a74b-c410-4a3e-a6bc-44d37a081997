/**
 * Company Demo Page
 * Trang demo để test form tạo công ty
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Card, Typography } from '@/shared/components/common';
import CompanyCreateForm from '../components/forms/CompanyCreateForm';

/**
 * Company Demo Page Component
 */
const CompanyDemoPage: React.FC = () => {
  const { t } = useTranslation(['business']);
  const [showForm, setShowForm] = useState(false);
  const [createdCompanyId, setCreatedCompanyId] = useState<string | null>(null);

  /**
   * Xử lý khi tạo công ty thành công
   */
  const handleSuccess = (companyId: string) => {
    console.log('Company created successfully with ID:', companyId);
    setCreatedCompanyId(companyId);
    setShowForm(false);
  };

  /**
   * <PERSON><PERSON> lý khi hủy tạo công ty
   */
  const handleCancel = () => {
    setShowForm(false);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h1">
            Demo Form Tạo Công ty
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            Trang demo để test form tạo công ty với API POST /company/create
          </Typography>
          
          {!showForm && (
            <Button
              variant="primary"
              onClick={() => setShowForm(true)}
            >
              Hiển thị Form Tạo Công ty
            </Button>
          )}

          {createdCompanyId && (
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Typography variant="body2" className="text-green-700 dark:text-green-300">
                ✅ Đã tạo công ty thành công với ID: <strong>{createdCompanyId}</strong>
              </Typography>
            </div>
          )}
        </div>
      </Card>

      {/* Form */}
      {showForm && (
        <CompanyCreateForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      )}

      {/* API Information */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h3">
            Thông tin API
          </Typography>
          
          <div className="space-y-2">
            <Typography variant="body2">
              <strong>Endpoint:</strong> POST /company/create
            </Typography>
            <Typography variant="body2">
              <strong>Request Body:</strong>
            </Typography>
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm">
{`{
  "full_name": "string (required, max 200 chars)",
  "short_name": "string (required, max 20 chars)"
}`}
            </pre>
            
            <Typography variant="body2">
              <strong>Response:</strong>
            </Typography>
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm">
{`{
  "code": 201,
  "message": "Đã tạo công ty (tổ chức) thành công.",
  "id": "<CREATED_COMPANY_ID>"
}`}
            </pre>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CompanyDemoPage;
