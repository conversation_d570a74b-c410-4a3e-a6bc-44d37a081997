import React from 'react';
import { useTranslation } from 'react-i18next';
import KienLongBankAccountForm from '../components/forms/KienLongBankAccountForm';
import { KienLongBankAccountFormValues } from '../types/banking.types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang liên kết tài khoản ngân hàng Kiên Long Bank
 */
const KienLongBankingPage: React.FC = () => {
  const { t } = useTranslation(['integrations', 'common']);

  // Xử lý submit form
  const handleSubmit = async (values: KienLongBankAccountFormValues) => {
    try {
      console.log('Kiên Long Bank Account Data:', values);

      // TODO: Gọi API để lưu thông tin tài khoản Kiên Long Bank
      // await createKienLongBankAccount(values);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('integrations:banking.kienlong.saveSuccess', 'Đã lưu thông tin tài khoản Kiên Long Bank thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Save Kiên Long Bank account error:', error);
      NotificationUtil.error({
        message: t('integrations:banking.kienlong.saveError', 'Có lỗi xảy ra khi lưu thông tin tài khoản Kiên Long Bank'),
        duration: 5000,
      });
      throw error; // Re-throw để form xử lý
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <KienLongBankAccountForm onSubmit={handleSubmit} />
    </div>
  );
};

export default KienLongBankingPage;
