import React from 'react';
import { useTranslation } from 'react-i18next';
import ACBBankAccountForm from '../components/forms/ACBBankAccountForm';
import { ACBBankAccountFormValues } from '../types/banking.types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang liên kết tài khoản ngân hàng ACB
 */
const ACBBankingPage: React.FC = () => {
  const { t } = useTranslation(['integrations', 'common']);

  // Xử lý submit form
  const handleSubmit = async (values: ACBBankAccountFormValues) => {
    try {
      console.log('ACB Bank Account Data:', values);

      // TODO: Gọi API để lưu thông tin tài khoản ACB
      // await createACBBankAccount(values);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('integrations:banking.acb.saveSuccess', '<PERSON><PERSON> lưu thông tin tài khoản ACB thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Save ACB account error:', error);
      NotificationUtil.error({
        message: t('integrations:banking.acb.saveError', 'Có lỗi xảy ra khi lưu thông tin tài khoản ACB'),
        duration: 5000,
      });
      throw error; // Re-throw để form xử lý
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <ACBBankAccountForm onSubmit={handleSubmit} />
    </div>
  );
};

export default ACBBankingPage;
