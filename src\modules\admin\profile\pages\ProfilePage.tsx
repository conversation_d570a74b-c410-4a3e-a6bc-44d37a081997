import React from 'react';
import { useTranslation } from 'react-i18next';
import { Loading, Typography } from '@/shared/components/common';
import { useCurrentUser } from '../hooks/useUser';
import ProfileHeader from '../components/ProfileHeader';
import PersonalInfoForm from '../components/PersonalInfoForm';
import SecurityForm from '../components/SecurityForm';

import NotificationProvider from '../components/NotificationProvider';

import { ProfileAccordionProvider } from '../contexts/ProfileAccordionProvider.tsx';

/**
 * Trang profile user
 */
const ProfilePage: React.FC = () => {
  const { t } = useTranslation(['profile']);

  // Sử dụng hook để lấy dữ liệu người dùng
  const { data: user, isLoading, error: userError } = useCurrentUser();

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (userError || !user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Typography variant="h5" color="error">
          {t('profile:messages.loadingError')}
        </Typography>
      </div>
    );
  }

  return (
    <NotificationProvider>
      <ProfileAccordionProvider>
        <div>
          {/* Header với banner và avatar */}
          <ProfileHeader />

          {/* Nội dung chính */}
          <div className="py-8">
            {/* Thông tin cá nhân */}
            <PersonalInfoForm />

            {/* Bảo mật */}
            <SecurityForm />
          </div>
        </div>
      </ProfileAccordionProvider>
    </NotificationProvider>
  );
};

export default ProfilePage;
