/**
 * Company Hooks
 * Hooks sử dụng TanStack Query cho Company APIs
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createCompanyWithBusinessLogic, getCompanyWithBusinessLogic } from '../services/company.service';
import { CreateCompanyDto, CreateCompanyResponseDto, CompanyDto } from '../types/company.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Query Keys cho Company
 */
export const COMPANY_QUERY_KEYS = {
  ALL: ['companies'] as const,
  DETAIL: (id: string) => [...COMPANY_QUERY_KEYS.ALL, 'detail', id] as const,
} as const;

/**
 * Hook để tạo công ty mới
 * @returns Mutation object với các method và state
 */
export const useCreateCompany = () => {
  const queryClient = useQueryClient();

  return useMutation<
    ApiResponseDto<CreateCompanyResponseDto>,
    <PERSON><PERSON><PERSON>,
    CreateCompanyDto
  >({
    mutationFn: createCompanyWithBusinessLogic,
    onSuccess: (data) => {
      // Invalidate và refetch các queries liên quan
      queryClient.invalidateQueries({
        queryKey: COMPANY_QUERY_KEYS.ALL,
      });

      // Có thể cache thông tin công ty vừa tạo nếu cần
      if (data.result?.id) {
        queryClient.setQueryData(
          COMPANY_QUERY_KEYS.DETAIL(data.result.id),
          {
            result: {
              id: data.result.id,
              full_name: '', // Sẽ được fetch lại từ API
              short_name: '',
              created_at: new Date().toISOString(),
            } as CompanyDto,
          }
        );
      }
    },
    onError: (error) => {
      console.error('Error creating company:', error);
    },
  });
};

/**
 * Hook để lấy thông tin công ty theo ID
 * @param id ID của công ty
 * @param options Các tùy chọn cho query
 * @returns Query object với data và state
 */
export const useCompany = (
  id: string,
  options?: {
    enabled?: boolean;
    staleTime?: number;
  }
) => {
  return useQuery<ApiResponseDto<CompanyDto>, Error>({
    queryKey: COMPANY_QUERY_KEYS.DETAIL(id),
    queryFn: () => getCompanyWithBusinessLogic(id),
    enabled: !!id && (options?.enabled !== false),
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Không retry nếu là lỗi 404 (không tìm thấy)
      if (error.message.includes('404')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
