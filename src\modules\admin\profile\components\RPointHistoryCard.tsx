import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, Icon, Typography } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { formatDate } from '@/shared/utils';
import {
  useTransactionHistory,
  TransactionStatus,
  UserTransactionResponseDto,
  UserTransactionQueryDto,
} from '@/modules/rpoint';
import { useProfileAccordion } from '../hooks/useProfileAccordion';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import ProfileCard from './ProfileCard';

// Sử dụng các types từ module rpoint

/**
 * Component hiển thị lịch sử nạp R-Point
 */
const RPointHistoryCard: React.FC = () => {
  const { t } = useTranslation(['profile', 'common']);
  const { isCardOpen } = useProfileAccordion();

  // Kiểm tra xem card có đang mở không để lazy load
  const isCardOpened = isCardOpen(PROFILE_CARD_IDS.RPOINT_HISTORY);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<UserTransactionResponseDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'pointsAmount',
        title: (
          <div className="flex items-center">
            <span>{t('profile:rpoint.amount')}</span>
            <Icon name="rpoint" size="sm" className="ml-1 text-red-600" />
          </div>
        ),
        dataIndex: 'pointsAmount',
        width: '20%',
        sortable: true,
        render: (value: unknown) => {
          return (
            <div className="text-left">
              <span>{(value as number).toLocaleString()}</span>
            </div>
          );
        },
      },
      {
        key: 'amount',
        title: <div className="text-right">{t('common:amount')}</div>,
        dataIndex: 'amount',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-right">{(value as number).toLocaleString()}</div>;
        },
      },
      {
        key: 'paymentMethod',
        title: t('profile:rpoint.paymentMethod'),
        dataIndex: 'paymentMethod',
        width: '15%',
        sortable: true,
      },
      {
        key: 'status',
        title: t('profile:rpoint.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          // Chuyển đổi giá trị status thành chữ thường để khớp với enum TransactionStatus
          const statusStr = String(value).toLowerCase();
          let statusClass = '';
          let statusText = '';

          // Sử dụng statusStr thay vì status để so sánh
          switch (statusStr) {
            case TransactionStatus.CONFIRMED:
              statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
              statusText = t('common:confirmed');
              break;
            case TransactionStatus.PENDING:
              statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
              statusText = t('common:pending');
              break;
            case TransactionStatus.FAILED:
              statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
              statusText = t('common:failed');
              break;
            case TransactionStatus.CANCELED:
              statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              statusText = t('common:canceled');
              break;
            case TransactionStatus.REFUNDED:
              statusClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
              statusText = t('common:refunded');
              break;
            default: {
              // Xử lý trường hợp API trả về giá trị chữ hoa
              const upperCaseStatus = String(value).toUpperCase();
              if (upperCaseStatus === 'CONFIRMED') {
                statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                statusText = t('common:confirmed');
              } else if (upperCaseStatus === 'PENDING') {
                statusClass =
                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                statusText = t('common:pending');
              } else if (upperCaseStatus === 'FAILED') {
                statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                statusText = t('common:failed');
              } else if (upperCaseStatus === 'CANCELED') {
                statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
                statusText = t('common:canceled');
              } else if (upperCaseStatus === 'REFUNDED') {
                statusClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                statusText = t('common:refunded');
              } else {
                statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
                statusText = String(value);
              }
            }
          }

          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${statusClass}`}
            >
              {statusText}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          try {
            // Chuyển đổi timestamp thành đối tượng Date
            // Nếu timestamp là string, chuyển đổi thành number
            const timestamp = typeof value === 'string' ? parseInt(value, 10) : (value as number);
            const date = new Date(timestamp);

            // Kiểm tra xem date có hợp lệ không
            if (isNaN(date.getTime())) {
              return <div>{String(value)}</div>;
            }

            return <div>{formatDate(date)}</div>;
          } catch (error) {
            console.error('Error formatting date:', error, value);
            return <div>{String(value)}</div>;
          }
        },
      },
    ],
    [t]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: TransactionStatus.CONFIRMED,
        label: t('common:confirmed'),
        icon: 'check',
        value: TransactionStatus.CONFIRMED,
      },
      {
        id: TransactionStatus.PENDING,
        label: t('common:pending'),
        icon: 'clock',
        value: TransactionStatus.PENDING,
      },
      {
        id: TransactionStatus.FAILED,
        label: t('common:failed'),
        icon: 'x',
        value: TransactionStatus.FAILED,
      },
      {
        id: TransactionStatus.CANCELED,
        label: t('common:canceled'),
        icon: 'x-circle',
        value: TransactionStatus.CANCELED,
      },
      {
        id: TransactionStatus.REFUNDED,
        label: t('common:refunded'),
        icon: 'refresh-cw',
        value: TransactionStatus.REFUNDED,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): UserTransactionQueryDto => {
    const queryParams: UserTransactionQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || '',
      sortBy: params.sortBy || '',
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      // API yêu cầu status là chữ hoa
      const statusValue = params.filterValue as string;
      // Chuyển đổi giá trị status thành TransactionStatus
      // Sử dụng type assertion để chuyển đổi string thành TransactionStatus
      queryParams.status = statusValue.toUpperCase() as unknown as TransactionStatus;
    }

    // Thêm ngày bắt đầu và kết thúc nếu có
    if (params.dateRange[0]) {
      queryParams.startTime = params.dateRange[0].getTime();
    }
    if (params.dateRange[1]) {
      queryParams.endTime = params.dateRange[1].getTime();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<UserTransactionResponseDto, UserTransactionQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API để lấy dữ liệu lịch sử giao dịch - chỉ khi card được mở
  const { data: transactionResponse, isLoading } = useTransactionHistory(
    dataTable.queryParams,
    { enabled: isCardOpened }
  );

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      confirmed: t('common:confirmed'),
      pending: t('common:pending'),
      failed: t('common:failed'),
      canceled: t('common:canceled'),
      refunded: t('common:refunded'),
    },
    t,
  });

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="credit-card" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:rpoint.historyTitle')}
      </Typography>
    </div>
  );

  // Nếu card chưa được mở, chỉ hiển thị card rỗng
  if (!isCardOpened) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.RPOINT_HISTORY} title={cardTitle}>
        <div></div>
      </ProfileCard>
    );
  }

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.RPOINT_HISTORY} title={cardTitle}>
      <div>
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          items={dataTable.menuItems}
          onDateRangeChange={dataTable.dateRange.setDateRange}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={true}
          showColumnFilter={true}
        />

        {/* Thêm component ActiveFilters */}
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          dateRange={dataTable.dateRange.dateRange}
          onClearDateRange={handleClearDateRange}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />

        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={transactionResponse?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: transactionResponse?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: transactionResponse?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </div>
    </ProfileCard>
  );
};

export default RPointHistoryCard;
