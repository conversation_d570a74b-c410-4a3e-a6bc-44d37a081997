# Company Form Implementation Guide

## Tổng quan

Đã tạo thành công trang form tạo công ty theo yêu cầu với đầy đủ các thành phần:

- ✅ API Service layer
- ✅ TanStack Query hooks
- ✅ Form component với validation
- ✅ Page component
- ✅ Router configuration
- ✅ Internationalization (i18n)
- ✅ TypeScript types
- ✅ Demo page để test

## Cấu trúc Files

```
src/modules/business/
├── types/
│   └── company.types.ts              # TypeScript interfaces
├── services/
│   └── company.service.ts            # API service layer
├── hooks/
│   └── useCompany.ts                 # TanStack Query hooks
├── components/forms/
│   └── CompanyCreateForm.tsx         # Form component
├── pages/
│   ├── CompanyCreatePage.tsx         # Main page
│   └── CompanyDemoPage.tsx           # Demo page
├── locales/
│   └── vi.json                       # Vietnamese translations
└── routers/
    └── businessRouters.tsx           # Router configuration
```

## API Specification

### Endpoint
```
POST /company/create
```

### Request Body
```json
{
  "full_name": "string (required, max 200 chars)",
  "short_name": "string (required, max 20 chars)"
}
```

### Response
```json
{
  "code": 201,
  "message": "Đã tạo công ty (tổ chức) thành công.",
  "id": "<CREATED_COMPANY_ID>"
}
```

## Routes

- **Main Form**: `/business/company/create`
- **Demo Page**: `/business/company/demo`

## Validation Rules

- `full_name`: Bắt buộc, tối đa 200 ký tự
- `short_name`: Bắt buộc, tối đa 20 ký tự

## Features

### Form Component (`CompanyCreateForm`)
- ✅ Zod schema validation
- ✅ useApiForm hook integration
- ✅ Loading states
- ✅ Error handling
- ✅ Success notifications
- ✅ Responsive design
- ✅ Internationalization

### API Integration
- ✅ 3-layer pattern (API → Service → Hook)
- ✅ TanStack Query for caching
- ✅ Error handling
- ✅ TypeScript types
- ✅ Business logic layer

### UI/UX
- ✅ Follows RedAI design patterns
- ✅ Card layout
- ✅ Typography components
- ✅ Form validation feedback
- ✅ Loading indicators
- ✅ Success/error notifications

## Usage Examples

### Basic Usage
```tsx
import { CompanyCreateForm } from '@/modules/business';

const MyPage = () => {
  const handleSuccess = (companyId: string) => {
    console.log('Company created:', companyId);
    // Navigate or show success message
  };

  return (
    <CompanyCreateForm
      onSuccess={handleSuccess}
      onCancel={() => navigate(-1)}
    />
  );
};
```

### Using the Hook Directly
```tsx
import { useCreateCompany } from '@/modules/business';

const MyComponent = () => {
  const createCompany = useCreateCompany();

  const handleSubmit = async (data) => {
    try {
      const result = await createCompany.mutateAsync(data);
      console.log('Success:', result);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <button 
      onClick={() => handleSubmit({ full_name: 'Test', short_name: 'T' })}
      disabled={createCompany.isPending}
    >
      {createCompany.isPending ? 'Creating...' : 'Create Company'}
    </button>
  );
};
```

## Testing

1. **Demo Page**: Truy cập `/business/company/demo` để test form
2. **Direct Access**: Truy cập `/business/company/create` để sử dụng form trực tiếp

## Translations

Tất cả text đã được internationalize với namespace `business:company`:

```json
{
  "business": {
    "company": {
      "title": "Quản lý công ty",
      "createTitle": "Tạo công ty mới",
      "form": {
        "fullName": "Tên đầy đủ công ty",
        "shortName": "Tên viết tắt công ty",
        "submit": "Tạo công ty"
      },
      "messages": {
        "createSuccess": "Đã tạo công ty (tổ chức) thành công"
      }
    }
  }
}
```

## Next Steps

1. **Backend Integration**: Đảm bảo API endpoint `/company/create` đã được implement
2. **Navigation**: Cập nhật menu/navigation để thêm link đến form
3. **Permissions**: Thêm kiểm tra quyền truy cập nếu cần
4. **Testing**: Viết unit tests cho components và hooks
5. **Company List**: Tạo trang danh sách công ty để quản lý các công ty đã tạo

## Architecture Notes

- Tuân thủ quy tắc phát triển frontend RedAI
- Sử dụng 3-layer API pattern (API → Service → Hook)
- Form validation với Zod
- TanStack Query cho state management
- TypeScript strict mode
- Responsive design
- Accessibility support
