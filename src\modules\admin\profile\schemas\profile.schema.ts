import { z } from 'zod';
import { Gender } from '../types/profile.types';

/**
 * Schema validation cho thông tin cá nhân
 */
export const personalInfoSchema = z.object({
  fullName: z
    .string()
    .min(2, { message: 'validation.fullName.min' })
    .max(100, { message: 'validation.fullName.max' }),
  gender: z.nativeEnum(Gender),
  birthDate: z.union([z.string(), z.date()]),
  address: z
    .string()
    .min(5, { message: 'validation.address.min' })
    .max(200, { message: 'validation.address.max' }),
  email: z.string().email({ message: 'validation.email.invalid' }),
  phone: z.string().regex(/^[0-9]{10,11}$/, { message: 'validation.phone.invalid' }),
});

/**
 * Schema validation cho thông tin bảo mật
 */
export const securityInfoSchema = z
  .object({
    googleAuthEnabled: z.boolean(),
    emailVerificationEnabled: z.boolean(),
    currentPassword: z
      .string()
      .min(1, { message: 'validation.currentPassword.required' })
      .optional(),
    newPassword: z
      .string()
      .min(8, { message: 'validation.password.min' })
      .regex(/[A-Z]/, { message: 'validation.password.uppercase' })
      .regex(/[a-z]/, { message: 'validation.password.lowercase' })
      .regex(/[0-9]/, { message: 'validation.password.number' })
      .regex(/[^A-Za-z0-9]/, { message: 'validation.password.special' })
      .optional(),
    confirmPassword: z.string().optional(),
  })
  .refine(
    data => {
      if (data.newPassword && !data.currentPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'validation.currentPassword.required',
      path: ['currentPassword'],
    }
  )
  .refine(
    data => {
      if (data.newPassword && data.confirmPassword && data.newPassword !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'validation.confirmPassword.match',
      path: ['confirmPassword'],
    }
  );

/**
 * Schema validation cho thông tin doanh nghiệp
 */
export const businessInfoSchema = z.object({
  companyName: z
    .string()
    .min(2, { message: 'validation.companyName.min' })
    .max(100, { message: 'validation.companyName.max' }),
  taxCode: z.string().regex(/^[0-9]{10,13}$/, { message: 'validation.taxCode.invalid' }),
  representative: z
    .string()
    .min(2, { message: 'validation.representative.min' })
    .max(100, { message: 'validation.representative.max' }),
  position: z
    .string()
    .min(2, { message: 'validation.position.min' })
    .max(100, { message: 'validation.position.max' }),
  address: z
    .string()
    .min(5, { message: 'validation.address.min' })
    .max(200, { message: 'validation.address.max' }),
  email: z.string().email({ message: 'validation.email.invalid' }),
  phone: z.string().regex(/^[0-9]{10,11}$/, { message: 'validation.phone.invalid' }),
});

/**
 * Schema validation cho thông tin ngân hàng
 */
export const bankInfoSchema = z.object({
  bankName: z.string().min(2, { message: 'validation.bankName.required' }),
  accountNumber: z.string().regex(/^[0-9]{8,20}$/, { message: 'validation.accountNumber.invalid' }),
  branch: z
    .string()
    .min(2, { message: 'validation.branch.min' })
    .max(100, { message: 'validation.branch.max' }),
  accountHolder: z
    .string()
    .min(2, { message: 'validation.accountHolder.min' })
    .max(100, { message: 'validation.accountHolder.max' }),
});

/**
 * Schema validation cho tùy chọn thông báo
 */
export const notificationOptionSchema = z.object({
  id: z.string(),
  name: z.string(),
  enabled: z.boolean(),
  description: z.string(),
});

/**
 * Schema validation cho tùy chọn thông báo
 */
export const notificationSettingsSchema = z.object({
  options: z.array(notificationOptionSchema),
});

/**
 * Schema validation cho profile đầy đủ
 */
export const userProfileSchema = z.object({
  id: z.string(),
  username: z.string(),
  avatarUrl: z.string(),
  coverImageUrl: z.string(),
  personalInfo: personalInfoSchema,
  securityInfo: securityInfoSchema,
  businessInfo: businessInfoSchema,
  bankInfo: bankInfoSchema,
  notificationSettings: notificationSettingsSchema,
});

// Export các type từ schema
export type PersonalInfoSchema = z.infer<typeof personalInfoSchema>;
export type SecurityInfoSchema = z.infer<typeof securityInfoSchema>;
export type BusinessInfoSchema = z.infer<typeof businessInfoSchema>;
export type BankInfoSchema = z.infer<typeof bankInfoSchema>;
export type NotificationSettingsSchema = z.infer<typeof notificationSettingsSchema>;
export type UserProfileSchema = z.infer<typeof userProfileSchema>;
