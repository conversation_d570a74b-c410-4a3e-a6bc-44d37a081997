import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Form,
  FormItem,
  Input,
  Toggle,
  Icon,
  CollapsibleCard,
  Typography,
  Loading,
  OTPInput,
} from '@/shared/components/common';
import { FormStatus } from '../types/profile.types';
import {
  useTwoFactorAuthStatus,
  useToggleSmsAuth,
  useToggleEmailAuth,
  useToggleGoogleAuth,
  useSetupGoogleAuth,
  useVerifyGoogleAuth,
  useChangePassword,
} from '../hooks/useUser';
import { useProfileNotification } from '../contexts/ProfileNotificationContext';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { useFormErrors } from '@/shared/hooks';
import { GoogleAuthSetupDto } from '../types/user.types';
import { createSecurityInfoSchema, SecurityInfoSchema } from '../schemas';
import ProfileCard from './ProfileCard';

// Interface cho lỗi API
interface ApiError {
  response?: {
    data?: {
      code?: number;
      message?: string;
      path?: string;
      requestId?: string;
    };
  };
  data?: {
    code?: number;
    message?: string;
  };
  code?: number;
  message?: string;
}

// CSS cho animation
const slideInFromRightKeyframes = `
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
`;

// Thêm keyframes vào document
const addKeyframesToDocument = () => {
  if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.textContent = slideInFromRightKeyframes;
    document.head.appendChild(styleElement);
    return () => {
      document.head.removeChild(styleElement);
    };
  }
  return () => {};
};

/**
 * Component form bảo mật
 */
const SecurityForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [showGoogleAuthSetup, setShowGoogleAuthSetup] = useState(false);
  const [googleAuthSetupData, setGoogleAuthSetupData] = useState<GoogleAuthSetupDto | null>(null);
  const [googleAuthOtp, setGoogleAuthOtp] = useState('');
  const [isSettingUpGoogleAuth, setIsSettingUpGoogleAuth] = useState(false);
  const [isVerifyingGoogleAuth, setIsVerifyingGoogleAuth] = useState(false);

  const { showNotification } = useProfileNotification();
  const { formRef, setFormErrors } = useFormErrors<SecurityInfoSchema>();

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ
  const securityInfoSchema = createSecurityInfoSchema(t);



  // Sử dụng hooks để lấy và cập nhật dữ liệu
  const { data: twoFactorAuth, isLoading, error } = useTwoFactorAuthStatus();
  const toggleSmsAuthMutation = useToggleSmsAuth();
  const toggleEmailAuthMutation = useToggleEmailAuth();
  const toggleGoogleAuthMutation = useToggleGoogleAuth();
  const setupGoogleAuthMutation = useSetupGoogleAuth();
  const verifyGoogleAuthMutation = useVerifyGoogleAuth();
  const changePasswordMutation = useChangePassword();

  // Thêm keyframes vào document
  useEffect(() => {
    const cleanup = addKeyframesToDocument();
    return cleanup;
  }, []);

  // Cập nhật form khi có dữ liệu từ API
  useEffect(() => {
    if (twoFactorAuth && formRef.current) {
      console.log('Updating form with 2FA data:', twoFactorAuth);
      formRef.current.reset({
        otpSmsEnabled: twoFactorAuth.otpSmsEnabled,
        otpEmailEnabled: twoFactorAuth.otpEmailEnabled,
        googleAuthenticatorEnabled: twoFactorAuth.googleAuthenticatorEnabled,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    }
  }, [twoFactorAuth, formRef]);

  // Xử lý khi submit form
  const handleSubmit = (data: SecurityInfoSchema) => {
    setFormStatus(FormStatus.SUBMITTING);

    // Xử lý thay đổi mật khẩu
    if (data.newPassword && data.currentPassword && data.confirmPassword) {
      console.log('Changing password...');
      changePasswordMutation.mutate(
        {
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
          confirmPassword: data.confirmPassword,
        },
        {
          onSuccess: response => {
            console.log('Change password success:', response);
            showNotification('success', t('profile:messages.passwordChangeSuccess'));
            setFormStatus(FormStatus.IDLE);
            // Reset password fields
            if (formRef.current) {
              formRef.current.reset({
                ...data,
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
              });
            }
          },
          onError: (error: ApiError) => {
            console.error('Change password error:', error);
            // Log chi tiết cấu trúc lỗi để debug
            console.log('Error structure:', JSON.stringify(error, null, 2));

            // Kiểm tra các cấu trúc lỗi có thể có
            const errorCode = error?.response?.data?.code || error?.data?.code || error?.code;

            const errorMessage =
              error?.response?.data?.message ||
              error?.data?.message ||
              error?.message ||
              'Mật khẩu hiện tại không chính xác';

            console.log('Extracted error code:', errorCode);
            console.log('Extracted error message:', errorMessage);

            // Kiểm tra nếu là lỗi mã 10011 (Mật khẩu hiện tại không chính xác)
            if (errorCode === 10011) {
              console.log('Detected error code 10011, setting form error');
              // Hiển thị lỗi dưới trường currentPassword
              setFormErrors({
                currentPassword: errorMessage,
              });
            } else {
              // Hiển thị thông báo lỗi chung
              showNotification('error', errorMessage);
            }

            setFormStatus(FormStatus.IDLE);
          },
        }
      );
    } else {
      setFormStatus(FormStatus.IDLE);
    }
  };

  // Xử lý khi thay đổi SMS 2FA
  const handleToggleSmsAuth = (enabled: boolean) => {
    if (!twoFactorAuth) return;

    console.log('Toggling SMS Auth:', enabled);
    toggleSmsAuthMutation.mutate(
      { enabled },
      {
        onSuccess: data => {
          console.log('Toggle SMS Auth Success:', data);
          showNotification('success', t('profile:messages.updateSuccess'));
        },
        onError: (error: ApiError) => {
          console.error('Toggle SMS Auth Error:', error);
          showNotification('error', t('profile:messages.updateError'));
        },
      }
    );
  };

  // Xử lý khi thay đổi Email 2FA
  const handleToggleEmailAuth = (enabled: boolean) => {
    if (!twoFactorAuth) return;

    console.log('Toggling Email Auth:', enabled);
    toggleEmailAuthMutation.mutate(
      { enabled },
      {
        onSuccess: data => {
          console.log('Toggle Email Auth Success:', data);
          showNotification('success', t('profile:messages.updateSuccess'));
        },
        onError: (error: ApiError) => {
          console.error('Toggle Email Auth Error:', error);
          showNotification('error', t('profile:messages.updateError'));
        },
      }
    );
  };

  // Xử lý khi hủy thay đổi mật khẩu
  const handleCancelPassword = () => {
    if (formRef.current) {
      formRef.current.reset({
        otpSmsEnabled: twoFactorAuth?.otpSmsEnabled || false,
        otpEmailEnabled: twoFactorAuth?.otpEmailEnabled || false,
        googleAuthenticatorEnabled: twoFactorAuth?.googleAuthenticatorEnabled || false,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    }
    setFormErrors({});
  };

  // Kiểm tra xem form có đang ở trạng thái chỉnh sửa không
  const isSubmitting = formStatus === FormStatus.SUBMITTING;

  // Toggle hiển thị mật khẩu
  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Xử lý khi nhấn nút cài đặt Google Authenticator
  const handleSetupGoogleAuth = () => {
    setIsSettingUpGoogleAuth(true);
    setupGoogleAuthMutation.mutate(undefined, {
      onSuccess: data => {
        setGoogleAuthSetupData(data);
        setShowGoogleAuthSetup(true);
        setIsSettingUpGoogleAuth(false);
      },
      onError: (error: ApiError) => {
        console.error('Setup Google Auth Error:', error);
        showNotification('error', t('profile:security.setupError'));
        setIsSettingUpGoogleAuth(false);
      },
    });
  };

  // Xử lý khi nhập OTP từ Google Authenticator
  const handleOtpChange = (otp: string) => {
    setGoogleAuthOtp(otp);
  };

  // Xử lý khi xác thực Google Authenticator
  const handleVerifyGoogleAuth = () => {
    if (googleAuthOtp.length !== 6) return;

    setIsVerifyingGoogleAuth(true);
    verifyGoogleAuthMutation.mutate(
      { token: googleAuthOtp },
      {
        onSuccess: () => {
          showNotification('success', t('profile:security.setupSuccess'));
          setShowGoogleAuthSetup(false);
          setGoogleAuthOtp('');
          setGoogleAuthSetupData(null);
          setIsVerifyingGoogleAuth(false);
        },
        onError: (error: ApiError) => {
          console.error('Verify Google Auth Error:', error);
          showNotification('error', t('profile:security.setupError'));
          setIsVerifyingGoogleAuth(false);
        },
      }
    );
  };

  // Xử lý khi hủy cài đặt Google Authenticator
  const handleCancelGoogleAuthSetup = () => {
    setShowGoogleAuthSetup(false);
    setGoogleAuthOtp('');
    setGoogleAuthSetupData(null);
  };

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="lock" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:security.title')}
      </Typography>
    </div>
  );

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.SECURITY} title={cardTitle}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
        </div>
      </ProfileCard>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error || !twoFactorAuth) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.SECURITY} title={cardTitle}>
        <div className="text-red-500 py-4">{t('profile:error.loadingSecuritySettings')}</div>
      </ProfileCard>
    );
  }

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.SECURITY} title={cardTitle}>
      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={securityInfoSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={{
          otpSmsEnabled: twoFactorAuth?.otpSmsEnabled || false,
          otpEmailEnabled: twoFactorAuth?.otpEmailEnabled || false,
          googleAuthenticatorEnabled: twoFactorAuth?.googleAuthenticatorEnabled || false,
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        }}
      >


        {/* SMS Authentication */}
        <FormItem name="otpSmsEnabled" label={t('profile:security.smsAuth')}>
          <Toggle
            checked={twoFactorAuth.otpSmsEnabled}
            onChange={checked => handleToggleSmsAuth(checked)}
          />
        </FormItem>

        {/* Email Authentication */}
        <FormItem name="otpEmailEnabled" label={t('profile:security.emailAuth')}>
          <Toggle
            checked={twoFactorAuth.otpEmailEnabled}
            onChange={checked => handleToggleEmailAuth(checked)}
          />
        </FormItem>

        {/* Google Authenticator */}
        <FormItem name="googleAuthenticatorEnabled" label={t('profile:security.googleAuth')}>
          <div className="flex items-center justify-between">
            <Toggle
              disabled={!twoFactorAuth.isGoogleAuthenticatorConfirmed}
              checked={twoFactorAuth.googleAuthenticatorEnabled}
              onChange={checked => {
                console.log('Google Auth Toggle changed to:', checked);
                if (twoFactorAuth.isGoogleAuthenticatorConfirmed) {
                  // Sử dụng mutation riêng cho Google Auth
                  toggleGoogleAuthMutation.mutate(
                    { enabled: checked },
                    {
                      onSuccess: data => {
                        console.log('Toggle Google Auth Success:', data);
                        showNotification('success', t('profile:messages.updateSuccess'));
                      },
                      onError: (error: ApiError) => {
                        console.error('Toggle Google Auth Error:', error);
                        showNotification('error', t('profile:messages.updateError'));
                      },
                    }
                  );
                }
              }}
            />
            {!twoFactorAuth.isGoogleAuthenticatorConfirmed && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleSetupGoogleAuth}
                isLoading={isSettingUpGoogleAuth}
                disabled={showGoogleAuthSetup}
                className="ml-4"
              >
                {t('profile:security.setupGoogleAuth')}
              </Button>
            )}
          </div>
        </FormItem>

        {/* Google Authenticator Setup */}
        {showGoogleAuthSetup && googleAuthSetupData && (
          <div
            className="mt-4 mb-6 transition-all duration-300 transform translate-x-0"
            style={{
              animation: 'slideInFromRight 0.3s ease-out forwards',
            }}
          >
            <CollapsibleCard
              title={t('profile:security.setupGoogleAuth')}
              className="mb-4"
              defaultOpen
            >
              <div className="space-y-6">
                {/* QR Code */}
                <div className="flex flex-col items-center">
                  <Typography variant="body1" className="mb-2">
                    {t('profile:security.scanQrCode')}
                  </Typography>
                  <div className="border border-border p-2 bg-white inline-block">
                    <img
                      src={googleAuthSetupData.qrCodeUrl}
                      alt="Google Authenticator QR Code"
                      className="w-48 h-48"
                    />
                  </div>
                </div>

                {/* Secret Key */}
                <div className="flex flex-col items-center">
                  <Typography variant="body1" className="mb-2">
                    {t('profile:security.secretKey')}
                  </Typography>
                  <div className="flex items-center bg-background-light p-2 rounded-md">
                    <Typography variant="body1" className="font-mono">
                      {googleAuthSetupData.secretKey}
                    </Typography>
                  </div>
                </div>

                {/* OTP Input */}
                <div className="mt-6">
                  <Typography variant="body1" className="mb-4 text-center">
                    {t('profile:security.enterOtp')}
                  </Typography>
                  <OTPInput
                    length={6}
                    onChange={handleOtpChange}
                    value={googleAuthOtp}
                    autoFocus
                    className="mb-6"
                  />
                </div>

                {/* Buttons */}
                <div className="flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={handleCancelGoogleAuthSetup}
                    disabled={isVerifyingGoogleAuth}
                  >
                    {t('profile:buttons.cancel')}
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleVerifyGoogleAuth}
                    isLoading={isVerifyingGoogleAuth}
                    disabled={googleAuthOtp.length !== 6}
                  >
                    {t('profile:security.verifyAndEnable')}
                  </Button>
                </div>
              </div>
            </CollapsibleCard>
          </div>
        )}

        <div className="mt-6 mb-4">
          <Typography variant="subtitle1" weight="semibold" color="dark">
            {t('profile:security.passwordSection')}
          </Typography>
        </div>

        {/* Mật khẩu hiện tại */}
        <FormItem name="currentPassword" label={t('profile:security.currentPassword')}>
          <Input
            type={showPassword.current ? 'text' : 'password'}
            placeholder={t('profile:security.currentPassword')}
            fullWidth
            rightIcon={
              <div className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" onClick={() => togglePasswordVisibility('current')}>
                <Icon name={showPassword.current ? 'eye-off' : 'eye'} size="sm" />
              </div>
            }
          />
        </FormItem>

        {/* Mật khẩu mới */}
        <FormItem name="newPassword" label={t('profile:security.newPassword')}>
          <Input
            type={showPassword.new ? 'text' : 'password'}
            placeholder={t('profile:security.newPassword')}
            fullWidth
            rightIcon={
              <div className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" onClick={() => togglePasswordVisibility('new')}>
                <Icon name={showPassword.new ? 'eye-off' : 'eye'} size="sm" />
              </div>
            }
          />
        </FormItem>

        {/* Xác nhận mật khẩu mới */}
        <FormItem name="confirmPassword" label={t('profile:security.confirmPassword')}>
          <Input
            type={showPassword.confirm ? 'text' : 'password'}
            placeholder={t('profile:security.confirmPassword')}
            fullWidth
            rightIcon={
              <div className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" onClick={() => togglePasswordVisibility('confirm')}>
                <Icon name={showPassword.confirm ? 'eye-off' : 'eye'} size="sm" />
              </div>
            }
          />
        </FormItem>

        {/* Buttons - Luôn hiển thị để test */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={handleCancelPassword}
            disabled={isSubmitting}
            type="button"
          >
            {t('profile:buttons.cancel')}
          </Button>
          <Button variant="primary" type="submit" isLoading={isSubmitting}>
            {t('profile:buttons.save')}
          </Button>
        </div>
      </Form>
    </ProfileCard>
  );
};

export default SecurityForm;
