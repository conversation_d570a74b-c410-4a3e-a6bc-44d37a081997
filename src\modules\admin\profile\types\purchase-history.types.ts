/**
 * Đ<PERSON><PERSON> tượng lịch sử mua hàng
 */
export interface PurchaseHistoryItem {
  /**
   * ID của giao dịch
   */
  id: number;

  /**
   * Ngày mua hàng
   */
  purchaseDate: string;

  /**
   * Số lượng/Giảm giá
   */
  amount: number;

  /**
   * Số điểm nhận được
   */
  points: number;

  /**
   * Trạng thái tăng/giảm
   */
  trend: 'up' | 'down';
}

/**
 * Kết quả phân trang cho lịch sử mua hàng
 */
export interface PaginatedPurchaseHistory {
  /**
   * Danh sách các mục lịch sử mua hàng
   */
  items: PurchaseHistoryItem[];

  /**
   * Tổng số mục
   */
  total: number;

  /**
   * Trang hiện tại
   */
  page: number;

  /**
   * Số mục trên mỗi trang
   */
  pageSize: number;
}

/**
 * Tham số truy vấn lịch sử mua hàng
 */
export interface PurchaseHistoryQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * S<PERSON> mục trên mỗi trang
   */
  pageSize?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Ngày bắt đầu
   */
  startDate?: string;

  /**
   * Ngày kết thúc
   */
  endDate?: string;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string;

  /**
   * Thứ tự sắp xếp
   */
  sortOrder?: 'asc' | 'desc';
}
