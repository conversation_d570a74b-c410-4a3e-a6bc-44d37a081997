import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { getPurchaseHistory } from '../services/purchase-history.service';
import { PurchaseHistoryQueryParams } from '../types/purchase-history.types';

// Key cho React Query
const PURCHASE_HISTORY_QUERY_KEY = 'purchaseHistory';

/**
 * Hook để lấy lịch sử mua hàng
 * @param params Tham số truy vấn
 */
export const usePurchaseHistory = (params: PurchaseHistoryQueryParams = {}) => {
  return useQuery({
    queryKey: [PURCHASE_HISTORY_QUERY_KEY, params],
    queryFn: () => getPurchaseHistory(params),
    placeholderData: keepPreviousData,
  });
};
