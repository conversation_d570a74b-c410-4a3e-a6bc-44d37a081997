import {
  PaginatedPurchaseHistory,
  PurchaseHistoryQueryParams,
} from '../types/purchase-history.types';

/**
 * L<PERSON><PERSON> lịch sử mua hàng của người dùng
 * @param params Tham số truy vấn
 * @returns Danh sách lịch sử mua hàng đã phân trang
 */
export const getPurchaseHistory = async (
  params: PurchaseHistoryQueryParams = {}
): Promise<PaginatedPurchaseHistory> => {
  // Trong môi trường thực tế, đây sẽ là một API call
  // Ở đây chúng ta giả lập dữ liệu

  // Tạo dữ liệu mẫu
  const mockData: PaginatedPurchaseHistory = {
    items: Array.from({ length: 20 }, (_, index) => ({
      id: index + 1,
      purchaseDate: '20/01/2023',
      amount: 10000,
      points: 1000000,
      trend: 'up',
    })),
    total: 20,
    page: params.page || 1,
    pageSize: params.pageSize || 5,
  };

  // Lọc theo từ khóa tìm kiếm nếu có
  if (params.search) {
    const searchLower = params.search.toLowerCase();
    mockData.items = mockData.items.filter(
      item => item.id.toString().includes(searchLower) || item.purchaseDate.includes(searchLower)
    );
    mockData.total = mockData.items.length;
  }

  // Lọc theo ngày nếu có
  if (params.startDate || params.endDate) {
    // Trong thực tế, cần chuyển đổi chuỗi ngày thành đối tượng Date để so sánh
    // Ở đây chúng ta giả định dữ liệu mẫu đã được lọc
  }

  // Sắp xếp nếu có
  if (params.sortBy) {
    const sortField = params.sortBy as keyof (typeof mockData.items)[0];
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1;

    mockData.items.sort((a, b) => {
      if (a[sortField] < b[sortField]) return -1 * sortOrder;
      if (a[sortField] > b[sortField]) return 1 * sortOrder;
      return 0;
    });
  }

  // Phân trang
  const startIndex = (mockData.page - 1) * mockData.pageSize;
  const endIndex = startIndex + mockData.pageSize;
  const paginatedItems = mockData.items.slice(startIndex, endIndex);

  return {
    ...mockData,
    items: paginatedItems,
  };
};
