/**
 * Company Types
 * Định nghĩa các kiểu dữ liệu cho module company
 */

/**
 * Interface cho dữ liệu tạo công ty
 */
export interface CreateCompanyDto {
  /** Tên đầy đủ công ty (tổ chức) */
  full_name: string;
  /** Tên viết tắt công ty (tổ chức) */
  short_name: string;
}

/**
 * Interface cho response khi tạo công ty thành công
 */
export interface CreateCompanyResponseDto {
  /** Mã trạng thái */
  code: number;
  /** Thông báo */
  message: string;
  /** ID của công ty vừa tạo */
  id: string;
}

/**
 * Interface cho thông tin công ty
 */
export interface CompanyDto {
  /** ID công ty */
  id: string;
  /** Tên đầy đủ công ty */
  full_name: string;
  /** Tên viết tắt công ty */
  short_name: string;
  /** Ngày tạo */
  created_at?: string;
  /** <PERSON><PERSON><PERSON> cập nhật */
  updated_at?: string;
}
