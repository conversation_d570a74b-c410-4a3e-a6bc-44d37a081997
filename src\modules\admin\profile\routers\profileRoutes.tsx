import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

import { Loading } from '@/shared/components/common';
import AdminLayout from '@/shared/layouts/AdminLayout';

// Import Profile pages
const ProfilePage = lazy(() => import('@/modules/profile/pages/ProfilePage'));

/**
 * Profile module routes
 */
const profileRoutes: RouteObject[] = [
  {
    path: '/admin/profile',
    element: (
      <AdminLayout title="Thông tin tài khoản">
        <Suspense fallback={<Loading />}>
          <ProfilePage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default profileRoutes;
