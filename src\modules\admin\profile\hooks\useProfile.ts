import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getProfile,
  updatePersonalInfo,
  updateSecurityInfo,
  updateBusinessInfo,
  updateBankInfo,
  updateNotificationSettings,
  updateAvatar,
  updateCoverImage,
} from '../services/profile.service';

// Key cho React Query
const PROFILE_QUERY_KEY = 'userProfile';

/**
 * Hook để lấy thông tin profile
 */
export const useProfile = () => {
  return useQuery({
    queryKey: [PROFILE_QUERY_KEY],
    queryFn: getProfile,
  });
};

/**
 * Hook để cập nhật thông tin cá nhân
 */
export const useUpdatePersonalInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updatePersonalInfo,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};

/**
 * Hook để cập nhật thông tin bảo mật
 */
export const useUpdateSecurityInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateSecurityInfo,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};

/**
 * Hook để cập nhật thông tin doanh nghiệp
 */
export const useUpdateBusinessInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBusinessInfo,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};

/**
 * Hook để cập nhật thông tin ngân hàng
 */
export const useUpdateBankInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBankInfo,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};

/**
 * Hook để cập nhật tùy chọn thông báo
 */
export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateNotificationSettings,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};

/**
 * Hook để cập nhật avatar
 */
export const useUpdateAvatar = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateAvatar,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};

/**
 * Hook để cập nhật ảnh bìa
 */
export const useUpdateCoverImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCoverImage,
    onSuccess: data => {
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
    },
  });
};
