import { z } from 'zod';
import { GenderEnum } from '../types/user.types';

/**
 * Schema validation cho thông tin cá nhân
 */
export const personalInfoSchema = z.object({
  fullName: z
    .string()
    .min(2, { message: 'validation.fullName.min' })
    .max(100, { message: 'validation.fullName.max' }),
  gender: z.nativeEnum(GenderEnum),
  dateOfBirth: z.union([z.string(), z.date()]),
  address: z
    .string()
    .min(5, { message: 'validation.address.min' })
    .max(200, { message: 'validation.address.max' }),
  phoneNumber: z.string().regex(/^[0-9]{10,11}$/, { message: 'validation.phone.invalid' }),
  citizenId: z
    .string()
    .min(9, { message: 'validation.citizenId.min' })
    .max(12, { message: 'validation.citizenId.max' })
    .optional(),
  citizenIssuePlace: z
    .string()
    .min(2, { message: 'validation.citizenIssuePlace.min' })
    .max(100, { message: 'validation.citizenIssuePlace.max' })
    .optional(),
  citizenIssueDate: z.union([z.string(), z.date()]).optional(),
});

/**
 * Schema validation cho thông tin bảo mật
 */
export const securityInfoSchema = z
  .object({
    currentPassword: z.string().min(8, { message: 'validation.password.min' }).optional(),
    newPassword: z.string().min(8, { message: 'validation.password.min' }).optional(),
    confirmPassword: z.string().min(8, { message: 'validation.password.min' }).optional(),
  })
  .refine(
    data => {
      if (data.newPassword && !data.currentPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'validation.currentPassword.required',
      path: ['currentPassword'],
    }
  )
  .refine(
    data => {
      if (data.newPassword && data.confirmPassword && data.newPassword !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'validation.confirmPassword.match',
      path: ['confirmPassword'],
    }
  );

/**
 * Schema validation cho thông tin doanh nghiệp
 */
export const businessInfoSchema = z.object({
  businessName: z
    .string()
    .min(2, { message: 'validation.businessName.min' })
    .max(255, { message: 'validation.businessName.max' }),
  businessEmail: z.string().email({ message: 'validation.email.invalid' }),
  businessPhone: z.string().regex(/^[0-9]{10,11}$/, { message: 'validation.phone.invalid' }),
  taxCode: z
    .string()
    .min(10, { message: 'validation.taxCode.min' })
    .max(13, { message: 'validation.taxCode.max' }),
  businessAddress: z
    .string()
    .min(5, { message: 'validation.address.min' })
    .max(1000, { message: 'validation.address.max' }),
  representativeName: z
    .string()
    .min(2, { message: 'validation.representativeName.min' })
    .max(100, { message: 'validation.representativeName.max' }),
  representativePosition: z
    .string()
    .min(2, { message: 'validation.representativePosition.min' })
    .max(255, { message: 'validation.representativePosition.max' }),
  businessRegistrationCertificate: z.string().optional(),
});

/**
 * Schema validation cho thông tin ngân hàng
 */
export const bankInfoSchema = z.object({
  bankCode: z
    .string()
    .min(2, { message: 'validation.bankCode.min' })
    .max(20, { message: 'validation.bankCode.max' }),
  accountNumber: z
    .string()
    .min(5, { message: 'validation.accountNumber.min' })
    .max(50, { message: 'validation.accountNumber.max' }),
  accountHolder: z
    .string()
    .min(2, { message: 'validation.accountHolder.min' })
    .max(255, { message: 'validation.accountHolder.max' }),
  bankBranch: z.string().max(255, { message: 'validation.bankBranch.max' }).optional(),
});

/**
 * Schema validation cho cài đặt thông báo
 */
export const notificationSettingsSchema = z.object({
  receiveAccountSystemEmails: z.boolean().optional(),
  receiveBillingEmails: z.boolean().optional(),
  receiveNewFeatureEmails: z.boolean().optional(),
  receiveAffiliateEmails: z.boolean().optional(),
  receiveDocumentationEmails: z.boolean().optional(),
  receivePromotionalEmails: z.boolean().optional(),
});

/**
 * Schema validation cho tải lên avatar
 */
export const avatarUploadSchema = z.object({
  imageType: z.string(),
  maxSize: z.number(),
});

/**
 * Schema validation cho cập nhật avatar
 */
export const updateAvatarSchema = z.object({
  avatarKey: z.string(),
});

/**
 * Schema validation cho bật/tắt xác thực hai lớp qua SMS
 */
export const toggleSmsAuthSchema = z.object({
  enabled: z.boolean(),
});

/**
 * Schema validation cho bật/tắt xác thực hai lớp qua email
 */
export const toggleEmailAuthSchema = z.object({
  enabled: z.boolean(),
});

/**
 * Schema validation cho xác nhận cài đặt Google Authenticator
 */
export const verifyGoogleAuthSchema = z.object({
  token: z.string().length(6, { message: 'validation.token.length' }),
});
