import { z } from 'zod';
import { TFunction } from 'i18next';

/**
 * Schema creator function for bank info form with translation support
 */
export const createBankInfoSchema = (t: TFunction) =>
  z.object({
    bankCode: z.string().min(1, t('validation:required')),
    accountNumber: z.string().min(1, t('validation:required')),
    accountHolder: z.string().min(1, t('validation:required')),
    bankBranch: z.string().optional(),
  });

/**
 * Type for bank info schema
 */
export type BankInfoSchema = z.infer<ReturnType<typeof createBankInfoSchema>>;
