import { apiClient } from '@/shared/api/axios';
import {
  UserProfile,
  UpdatePersonalInfoRequest,
  UpdateSecurityInfoRequest,
  UpdateBusinessInfoRequest,
  UpdateBankInfoRequest,
  UpdateNotificationSettingsRequest,
  UpdateAvatarRequest,
  UpdateCoverImageRequest,
  Gender,
} from '../types/profile.types';

// Mock data cho profile
const mockUserProfile: UserProfile = {
  id: 'user-1',
  username: 'johndo<PERSON>',
  avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
  coverImageUrl: 'https://picsum.photos/1200/300',
  personalInfo: {
    fullName: '<PERSON>',
    gender: Gender.MALE,
    birthDate: '1990-01-01',
    address: '123 Main St, City',
    email: '<EMAIL>',
    phone: '**********',
  },
  securityInfo: {
    googleAuthEnabled: false,
    emailVerificationEnabled: true,
  },
  businessInfo: {
    companyName: 'ABC Company',
    taxCode: '*********',
    representative: '<PERSON>',
    position: 'CEO',
    address: '456 Business St, City',
    email: '<EMAIL>',
    phone: '**********',
  },
  bankInfo: {
    bankName: 'Example Bank',
    accountNumber: '**********',
    branch: 'Main Branch',
    accountHolder: 'John Doe',
  },
  notificationSettings: {
    options: [
      {
        id: 'email',
        name: 'Email Notifications',
        enabled: true,
        description: 'Receive notifications via email',
      },
      {
        id: 'sms',
        name: 'SMS Notifications',
        enabled: false,
        description: 'Receive notifications via SMS',
      },
      {
        id: 'push',
        name: 'Push Notifications',
        enabled: true,
        description: 'Receive push notifications',
      },
    ],
  },
};

/**
 * API base URL
 */
const API_BASE_URL = '/users/profile';

/**
 * Lấy thông tin profile
 */
export const getProfile = async (): Promise<UserProfile> => {
  try {
    const response = await apiClient.get<UserProfile>(`${API_BASE_URL}`);
    return response.result;
  } catch (error) {
    console.error('Error fetching profile:', error);
    // Fallback to mock data if API fails
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật thông tin cá nhân
 */
export const updatePersonalInfo = async (
  request: UpdatePersonalInfoRequest
): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/personal-info`, request);
    return response.result;
  } catch (error) {
    console.error('Error updating personal info:', error);
    // Fallback to mock data if API fails
    mockUserProfile.personalInfo = {
      ...mockUserProfile.personalInfo,
      ...request.personalInfo,
    };
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật thông tin bảo mật
 */
export const updateSecurityInfo = async (
  request: UpdateSecurityInfoRequest
): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/security-info`, request);
    return response.result;
  } catch (error) {
    console.error('Error updating security info:', error);
    // Fallback to mock data if API fails
    mockUserProfile.securityInfo = {
      ...mockUserProfile.securityInfo,
      googleAuthEnabled: request.securityInfo.googleAuthEnabled,
      emailVerificationEnabled: request.securityInfo.emailVerificationEnabled,
    };
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật thông tin doanh nghiệp
 */
export const updateBusinessInfo = async (
  request: UpdateBusinessInfoRequest
): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/business-info`, request);
    return response.result;
  } catch (error) {
    console.error('Error updating business info:', error);
    // Fallback to mock data if API fails
    mockUserProfile.businessInfo = {
      ...mockUserProfile.businessInfo,
      ...request.businessInfo,
    };
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật thông tin ngân hàng
 */
export const updateBankInfo = async (request: UpdateBankInfoRequest): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/bank-info`, request);
    return response.result;
  } catch (error) {
    console.error('Error updating bank info:', error);
    // Fallback to mock data if API fails
    mockUserProfile.bankInfo = {
      ...mockUserProfile.bankInfo,
      ...request.bankInfo,
    };
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật tùy chọn thông báo
 */
export const updateNotificationSettings = async (
  request: UpdateNotificationSettingsRequest
): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(
      `${API_BASE_URL}/notification-settings`,
      request
    );
    return response.result;
  } catch (error) {
    console.error('Error updating notification settings:', error);
    // Fallback to mock data if API fails
    mockUserProfile.notificationSettings = {
      ...mockUserProfile.notificationSettings,
      ...request.notificationSettings,
    };
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật avatar
 */
export const updateAvatar = async (request: UpdateAvatarRequest): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/avatar`, request);
    return response.result;
  } catch {
    // Fallback to mock data if API fails
    mockUserProfile.avatarUrl = request.avatarUrl;
    return { ...mockUserProfile };
  }
};

/**
 * Cập nhật ảnh bìa
 */
export const updateCoverImage = async (request: UpdateCoverImageRequest): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/cover-image`, request);
    return response.result;
  } catch (error) {
    console.error('Error updating cover image:', error);
    // Fallback to mock data if API fails
    mockUserProfile.coverImageUrl = request.coverImageUrl;
    return { ...mockUserProfile };
  }
};
