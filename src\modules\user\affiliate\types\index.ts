// Affiliate Overview Types
export interface AffiliateOverviewStats {
  totalClicks: number;
  totalCustomers: number;
  totalOrders: number;
  totalRevenue: number;
}

export interface AffiliateCommissionData {
  month: string;
  commission: number;
  orders: number;
}

export interface AffiliateWallet {
  balance: number;
  pendingAmount: number;
  currency: string;
}

export interface AffiliateReferralLink {
  url: string;
  code: string;
  qrCode?: string;
}

export interface AffiliateCertificate {
  imageUrl: string;
  title: string;
  issuedDate: string;
}

export interface AffiliateOrder {
  id: string;
  orderCode: string;
  customerName: string;
  customerEmail: string;
  amount: number;
  commission: number;
  status: 'pending' | 'completed' | 'cancelled';
  createdAt: string;
}

export interface AffiliateWithdrawal {
  id: string;
  amount: number;
  bankAccount: string;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  requestedAt: string;
  processedAt?: string;
  note?: string;
}

export interface AffiliateCustomer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  totalOrders: number;
  totalSpent: number;
  registeredAt: string;
  lastOrderAt?: string;
}

export interface AffiliateContract {
  id: string;
  contractCode: string;
  contractType: string;
  commissionRate: number; // Percentage (e.g., 5.5 for 5.5%)
  startDate: string;
  endDate: string;
  status: 'draft' | 'pending' | 'active' | 'expired' | 'terminated';
  signedAt?: string;
  terms?: string;
  description?: string;
}

export interface AffiliatePointConversion {
  id: string;
  points: number;
  convertedAmount: number;
  exchangeRate: number; // Points per VND (e.g., 1000 points = 1000 VND)
  status: 'pending' | 'completed' | 'failed';
  convertedAt: string;
  completedAt?: string;
  note?: string;
}

export interface AffiliatePaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AffiliateOverviewData {
  stats: AffiliateOverviewStats;
  commissionChart: AffiliateCommissionData[];
  wallet: AffiliateWallet;
  referralLink: AffiliateReferralLink;
  certificate?: AffiliateCertificate;
  recentOrders: AffiliateOrder[];
  recentWithdrawals: AffiliateWithdrawal[];
  recentCustomers: AffiliateCustomer[];
}
