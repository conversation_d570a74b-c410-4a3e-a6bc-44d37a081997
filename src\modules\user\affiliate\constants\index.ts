/**
 * Query keys cho TanStack Query
 */
export const AFFILIATE_QUERY_KEYS = {
  ALL: ['affiliate'] as const,
  OVERVIEW: () => [...AFFILIATE_QUERY_KEYS.ALL, 'overview'] as const,
  ORDERS: (params: Record<string, unknown>) =>
    [...AFFILIATE_QUERY_KEYS.ALL, 'orders', params] as const,
  WITHDRAWALS: (params: Record<string, unknown>) =>
    [...AFFILIATE_QUERY_KEYS.ALL, 'withdrawals', params] as const,
  CUSTOMERS: (params: Record<string, unknown>) =>
    [...AFFILIATE_QUERY_KEYS.ALL, 'customers', params] as const,
  CONTRACTS: (params: Record<string, unknown>) =>
    [...AFFILIATE_QUERY_KEYS.ALL, 'contracts', params] as const,
  POINT_CONVERSIONS: (params: Record<string, unknown>) =>
    [...AFFILIATE_QUERY_KEYS.ALL, 'point-conversions', params] as const,
};

/**
 * Các hằng số cho affiliate
 */
export const AFFILIATE_CONSTANTS = {
  MIN_WITHDRAWAL_AMOUNT: 100000, // 100k VND
  MAX_WITHDRAWAL_AMOUNT: 50000000, // 50M VND
  MIN_POINTS_CONVERSION: 100,
  POINTS_TO_MONEY_RATE: 1000, // 1000 points = 1000 VND
} as const;

/**
 * Status mapping cho hiển thị
 */
export const ORDER_STATUS_MAP = {
  pending: { label: 'Chờ xử lý', color: 'warning' },
  completed: { label: 'Hoàn thành', color: 'success' },
  cancelled: { label: 'Đã hủy', color: 'danger' },
} as const;

export const WITHDRAWAL_STATUS_MAP = {
  pending: { label: 'Chờ duyệt', color: 'warning' },
  processing: { label: 'Đang xử lý', color: 'info' },
  completed: { label: 'Hoàn thành', color: 'success' },
  rejected: { label: 'Từ chối', color: 'danger' },
} as const;

export const CONTRACT_STATUS_MAP = {
  draft: { label: 'Bản nháp', color: 'secondary' },
  pending: { label: 'Chờ ký', color: 'warning' },
  active: { label: 'Đang hiệu lực', color: 'success' },
  expired: { label: 'Hết hạn', color: 'danger' },
  terminated: { label: 'Đã chấm dứt', color: 'danger' },
} as const;

export const POINT_CONVERSION_STATUS_MAP = {
  pending: { label: 'Chờ xử lý', color: 'warning' },
  completed: { label: 'Hoàn thành', color: 'success' },
  failed: { label: 'Thất bại', color: 'danger' },
} as const;
