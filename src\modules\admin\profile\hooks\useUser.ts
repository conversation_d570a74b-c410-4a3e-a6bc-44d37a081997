import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getCurrentUser,
  getBusinessInfo,
  getBankInfo,
  updatePersonalInfo,
  updateBusinessInfo,
  updateBankInfo,
  createAvatarUploadUrl,
  updateAvatar,
  getTwoFactorAuthStatus,
  toggleSmsAuth,
  toggleEmailAuth,
  toggleGoogleAuth,
  setupGoogleAuth,
  verifyGoogleAuth,
  getNotificationSettings,
  updateNotificationSettings,
  getUserPoints,
  getBanks,
  getAllBanks,
  updateCoverImage,
  changePassword,
} from '../services/user.service';
import {
  AvatarUploadDto,
  UpdateAvatarDto,
  ToggleSmsAuthDto,
  ToggleEmailAuthDto,
  VerifyGoogleAuthDto,
  UpdateNotificationSettingsDto,
  BankQueryDto,
  ChangePasswordDto,
} from '../types/user.types';

// Keys cho React Query
export const USER_QUERY_KEYS = {
  profile: ['user', 'profile'],
  businessInfo: ['user', 'businessInfo'],
  bankInfo: ['user', 'bankInfo'],
  twoFactorAuth: ['user', '2fa'],
  notificationSettings: ['user', 'notificationSettings'],
  points: ['user', 'points'],
  banks: ['banks'],
};

/**
 * Hook để lấy thông tin người dùng hiện tại
 */
export const useCurrentUser = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.profile,
    queryFn: getCurrentUser,
  });
};

/**
 * Hook để cập nhật thông tin cá nhân
 */
export const useUpdatePersonalInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updatePersonalInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
    },
  });
};

/**
 * Hook để lấy thông tin doanh nghiệp
 */
export const useBusinessInfo = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.businessInfo,
    queryFn: getBusinessInfo,
    enabled: options?.enabled !== undefined ? options.enabled : true,
  });
};

/**
 * Hook để cập nhật thông tin doanh nghiệp
 */
export const useUpdateBusinessInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBusinessInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.businessInfo });
    },
  });
};

/**
 * Hook để lấy thông tin ngân hàng
 */
export const useBankInfo = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.bankInfo,
    queryFn: getBankInfo,
    enabled: options?.enabled !== undefined ? options.enabled : true,
  });
};

/**
 * Hook để cập nhật thông tin ngân hàng
 */
export const useUpdateBankInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBankInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.bankInfo });
    },
  });
};

/**
 * Hook để lấy danh sách ngân hàng
 * Dữ liệu ngân hàng thay đổi rất ít, nên cache lâu dài
 * @param query Query parameters
 * @param options Options cho useQuery
 */
export const useBanks = (
  query: BankQueryDto,
  options?: { enabled?: boolean; staleTime?: number; gcTime?: number }
) => {
  // Thời gian cache mặc định: 1 ngày cho staleTime, 7 ngày cho gcTime
  const ONE_DAY = 24 * 60 * 60 * 1000;
  const SEVEN_DAYS = 7 * ONE_DAY;

  return useQuery({
    queryKey: [...USER_QUERY_KEYS.banks, query],
    queryFn: () => getBanks(query),
    enabled: options?.enabled !== undefined ? options.enabled : false, // Mặc định không tự động tải
    staleTime: options?.staleTime || ONE_DAY, // Cache 1 ngày trước khi refetch
    gcTime: options?.gcTime || SEVEN_DAYS, // Giữ trong cache 7 ngày
  });
};

/**
 * Hook để lấy tất cả ngân hàng (không phân trang)
 * Dữ liệu ngân hàng thay đổi rất ít, nên cache lâu dài
 */
export const useAllBanks = () => {
  // Thời gian cache mặc định: 1 ngày cho staleTime, 7 ngày cho gcTime
  const ONE_DAY = 24 * 60 * 60 * 1000;
  const SEVEN_DAYS = 7 * ONE_DAY;

  return useQuery({
    queryKey: [...USER_QUERY_KEYS.banks, 'all'],
    queryFn: getAllBanks,
    staleTime: ONE_DAY, // Cache 1 ngày trước khi refetch
    gcTime: SEVEN_DAYS, // Giữ trong cache 7 ngày
  });
};

/**
 * Hook để tạo URL tạm thời để tải lên avatar
 */
export const useCreateAvatarUploadUrl = () => {
  return useMutation({
    mutationFn: (data: AvatarUploadDto) => createAvatarUploadUrl(data),
  });
};

/**
 * Hook để cập nhật avatar
 */
export const useUpdateAvatar = () => {
  const queryClient = useQueryClient();
  const PROFILE_QUERY_KEY = 'userProfile';

  return useMutation({
    mutationFn: (data: UpdateAvatarDto) => updateAvatar(data),
    onSuccess: () => {
      // Invalidate both user profile and userProfile queries
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
      queryClient.invalidateQueries({ queryKey: [PROFILE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để lấy trạng thái xác thực hai yếu tố
 */
export const useTwoFactorAuthStatus = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.twoFactorAuth,
    queryFn: getTwoFactorAuthStatus,
    enabled: options?.enabled !== undefined ? options.enabled : true,
  });
};

/**
 * Hook để bật/tắt xác thực hai lớp qua SMS
 */
export const useToggleSmsAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ToggleSmsAuthDto) => toggleSmsAuth(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để bật/tắt xác thực hai lớp qua email
 */
export const useToggleEmailAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ToggleEmailAuthDto) => toggleEmailAuth(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để bật/tắt xác thực hai lớp qua Google Authenticator
 */
export const useToggleGoogleAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ToggleEmailAuthDto) => toggleGoogleAuth(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để thiết lập Google Authenticator
 */
export const useSetupGoogleAuth = () => {
  return useMutation({
    mutationFn: setupGoogleAuth,
  });
};

/**
 * Hook để xác nhận cài đặt Google Authenticator
 */
export const useVerifyGoogleAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: VerifyGoogleAuthDto) => verifyGoogleAuth(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để lấy cài đặt thông báo
 */
export const useNotificationSettings = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.notificationSettings,
    queryFn: getNotificationSettings,
    enabled: options?.enabled !== undefined ? options.enabled : true,
  });
};

/**
 * Hook để cập nhật cài đặt thông báo
 */
export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateNotificationSettingsDto) => updateNotificationSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.notificationSettings });
    },
  });
};

/**
 * Hook để lấy số point của người dùng hiện tại
 */
export const useUserPoints = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.points,
    queryFn: getUserPoints,
    // Đảm bảo dữ liệu trả về là số
    select: data => {
      console.log('useUserPoints data:', data); // Log để debug
      if (typeof data === 'number' && !isNaN(data)) {
        return data;
      }
      return 0;
    },
  });
};
/**
 * Hook để cập nhật ảnh bìa
 */
export const useUpdateCoverImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCoverImage,
    onSuccess: data => {
      queryClient.setQueryData([USER_QUERY_KEYS], data);
    },
  });
};

/**
 * Hook để đổi mật khẩu
 */
export const useChangePassword = () => {
  return useMutation({
    mutationFn: (data: ChangePasswordDto) => changePassword(data),
  });
};
