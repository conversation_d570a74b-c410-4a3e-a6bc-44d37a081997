// Simplified version - no API calls, just mock data and functions

export const useCurrentUser = () => {
  // Mock user data
  const mockUser = {
    fullName: '<PERSON>uyễ<PERSON>n <PERSON>',
    gender: 'MALE',
    dateOfBirth: '1990-01-01',
    address: '123 Đường ABC, Quận 1, TP.HCM',
    email: '<EMAIL>',
    phoneNumber: '0123456789',
    isVerifyEmail: true,
    isVerifyPhone: true,
  };

  return {
    data: mockUser,
    isLoading: false,
    error: null,
  };
};

export const useUpdatePersonalInfo = () => {
  return {
    mutate: (data: any, options?: any) => {
      console.log('Mock update personal info:', data);
      if (options?.onSuccess) {
        setTimeout(() => options.onSuccess(data), 500);
      }
    },
    isLoading: false,
  };
};
