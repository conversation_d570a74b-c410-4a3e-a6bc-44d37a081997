import ProfilePage from './pages/ProfilePage';
import PurchaseHistoryPage from './pages/PurchaseHistoryPage';
import profileResources from './locales';
import { profileRoutes } from './routers';

// Export types
export * from './types/user.types';
export * from './types/profile.types';
export * from './types/purchase-history.types';

// Export schemas
export {
  personalInfoSchema,
  businessInfoSchema,
  bankInfoSchema,
  securityInfoSchema,
  notificationSettingsSchema,
} from './schemas/user.schema';

// Export services
export {
  getCurrentUser,
  updatePersonalInfo,
  getBusinessInfo,
  updateBusinessInfo,
  getBankInfo,
  updateBankInfo,
  createAvatarUploadUrl,
  updateAvatar,
  getTwoFactorAuthStatus,
  toggleSmsAuth,
  toggleEmailAuth,
  setupGoogleAuth,
  verifyGoogleAuth,
  getNotificationSettings,
  updateNotificationSettings,
  getUserPoints,
  getBanks,
} from './services/user.service';

// Export hooks
export {
  useCurrentUser,
  useUpdatePersonalInfo,
  useBusinessInfo,
  useUpdateBusinessInfo,
  useBankInfo,
  useUpdateBankInfo,
  useBanks,
  useCreateAvatarUploadUrl,
  useUpdateAvatar,
  useTwoFactorAuthStatus,
  useToggleSmsAuth,
  useToggleEmailAuth,
  useSetupGoogleAuth,
  useVerifyGoogleAuth,
  useNotificationSettings,
  useUpdateNotificationSettings,
  useUserPoints,
} from './hooks/useUser';

// Export purchase history services
export { getPurchaseHistory } from './services/purchase-history.service';

// Export purchase history hooks
export { usePurchaseHistory } from './hooks/usePurchaseHistory';

export { ProfilePage, PurchaseHistoryPage, profileResources, profileRoutes };
