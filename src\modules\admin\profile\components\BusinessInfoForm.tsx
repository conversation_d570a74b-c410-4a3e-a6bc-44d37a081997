import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Form,
  FormItem,
  Input,
  Icon,
  Textarea,
  Typography,
  Loading,
} from '@/shared/components/common';
import { FormStatus } from '../types/profile.types';
import { useBusinessInfo, useUpdateBusinessInfo } from '../hooks/useUser';
import { useProfileNotification } from '../contexts/ProfileNotificationContext';
import { useProfileAccordion } from '../hooks/useProfileAccordion';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { createBusinessInfoSchema, BusinessInfoSchema } from '../schemas';
import ProfileCard from './ProfileCard';

/**
 * Component form thông tin doanh nghiệp
 */
const BusinessInfoForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const { showNotification } = useProfileNotification();
  const { isCardOpen } = useProfileAccordion();

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ
  const businessInfoSchema = createBusinessInfoSchema(t);

  // Kiểm tra xem card có đang mở không để lazy load
  const isCardOpened = isCardOpen(PROFILE_CARD_IDS.BUSINESS_INFO);

  // Sử dụng hooks để lấy và cập nhật dữ liệu - chỉ khi card được mở
  const { data: businessInfo, isLoading, error } = useBusinessInfo({
    enabled: isCardOpened,
  });
  const updateBusinessInfoMutation = useUpdateBusinessInfo();



  // Xử lý khi submit form
  const onSubmit = (data: BusinessInfoSchema) => {
    // Sử dụng dữ liệu từ form
    const submittingData: BusinessInfoSchema = data;

    // Kiểm tra dữ liệu trước khi gửi
    if (!submittingData.businessName) {
      showNotification(
        'error',
        t('profile:validation.businessNameRequired', 'Tên doanh nghiệp là bắt buộc')
      );
      return;
    }

    if (!submittingData.businessEmail) {
      showNotification(
        'error',
        t('profile:validation.businessEmailRequired', 'Email doanh nghiệp là bắt buộc')
      );
      return;
    }

    if (!submittingData.businessPhone) {
      showNotification(
        'error',
        t('profile:validation.businessPhoneRequired', 'Số điện thoại doanh nghiệp là bắt buộc')
      );
      return;
    }

    if (!submittingData.taxCode) {
      showNotification('error', t('profile:validation.taxCodeRequired', 'Mã số thuế là bắt buộc'));
      return;
    }

    if (!submittingData.businessAddress) {
      showNotification(
        'error',
        t('profile:validation.businessAddressRequired', 'Địa chỉ doanh nghiệp là bắt buộc')
      );
      return;
    }

    if (!submittingData.representativeName) {
      showNotification(
        'error',
        t('profile:validation.representativeNameRequired', 'Tên người đại diện là bắt buộc')
      );
      return;
    }

    if (!submittingData.representativePosition) {
      showNotification(
        'error',
        t('profile:validation.representativePositionRequired', 'Chức vụ người đại diện là bắt buộc')
      );
      return;
    }

    setFormStatus(FormStatus.SUBMITTING);

    updateBusinessInfoMutation.mutate({
      businessName: submittingData.businessName || '',
      taxCode: submittingData.taxCode || '',
      businessEmail: submittingData.businessEmail || '',
      businessPhone: submittingData.businessPhone || '',
      businessAddress: submittingData.businessAddress || '',
    }, {
      onSuccess: () => {
        setFormStatus(FormStatus.IDLE);



        showNotification('success', t('profile:messages.updateSuccess'));
      },
      onError: error => {
        // Cập nhật trạng thái form
        setFormStatus(FormStatus.IDLE);

        // Hiển thị thông báo lỗi
        let errorMessage = t(
          'profile:messages.updateError',
          'Có lỗi xảy ra khi cập nhật thông tin doanh nghiệp'
        );

        // Kiểm tra xem error có phải là AxiosError không
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: { message?: string } } };
          if (axiosError.response?.data?.message) {
            errorMessage = axiosError.response.data.message;
          }
        }

        showNotification('error', errorMessage);
      },
    });
  };

  // Xử lý khi hủy thay đổi (reset về giá trị ban đầu)
  const handleCancel = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Reset form về giá trị ban đầu từ businessInfo data
    // Sẽ được implement sau khi có form ref
  };

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="building" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:businessInfo.title')}
      </Typography>
    </div>
  );

  // Nếu card chưa được mở, chỉ hiển thị card rỗng
  if (!isCardOpened) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BUSINESS_INFO} title={cardTitle}>
        <div></div>
      </ProfileCard>
    );
  }

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BUSINESS_INFO} title={cardTitle}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
        </div>
      </ProfileCard>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BUSINESS_INFO} title={cardTitle}>
        <div className="text-red-500 py-4">{t('profile:error.loadingBusinessInfo')}</div>
      </ProfileCard>
    );
  }

  // Hiển thị thông báo nếu không có thông tin doanh nghiệp
  if (!businessInfo) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BUSINESS_INFO} title={cardTitle}>
        <div className="py-4">{t('profile:businessInfo.noBusinessInfo')}</div>
      </ProfileCard>
    );
  }

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.BUSINESS_INFO} title={cardTitle}>
      <Form
        schema={businessInfoSchema}
        defaultValues={{
          businessName: businessInfo.businessName || '',
          businessEmail: businessInfo.businessEmail || '',
          businessPhone: businessInfo.businessPhone || '',
          taxCode: businessInfo.taxCode || '',
          businessAddress: businessInfo.businessAddress || '',
          representativeName: businessInfo.representativeName || '',
          representativePosition: businessInfo.representativePosition || '',
          businessRegistrationCertificate: businessInfo.businessRegistrationCertificate || '',
        }}
        onSubmit={(data, e) => {
          // Kiểm tra xem form có đang ở trạng thái submitting không
          if (formStatus === FormStatus.SUBMITTING) {
            e?.preventDefault();
            return;
          }

          onSubmit(data as BusinessInfoSchema);
        }}

      >
        <div className="space-y-6">
          {/* Tên doanh nghiệp */}
          <div>
            <FormItem name="businessName" label={t('profile:businessInfo.companyName')}>
              <Input
                placeholder={t('profile:businessInfo.companyName')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Mã số thuế */}
          <div>
            <FormItem name="taxCode" label={t('profile:businessInfo.taxCode')}>
              <Input
                placeholder={t('profile:businessInfo.taxCode')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Người đại diện */}
          <div>
            <FormItem name="representativeName" label={t('profile:businessInfo.representative')}>
              <Input
                placeholder={t('profile:businessInfo.representative')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Chức vụ */}
          <div>
            <FormItem name="representativePosition" label={t('profile:businessInfo.position')}>
              <Input
                placeholder={t('profile:businessInfo.position')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Địa chỉ */}
          <div>
            <FormItem name="businessAddress" label={t('profile:businessInfo.address')}>
              <Textarea
                placeholder={t('profile:businessInfo.address')}
                rows={3}
                fullWidth
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </FormItem>
          </div>

          {/* Email */}
          <div>
            <FormItem name="businessEmail" label={t('profile:businessInfo.email')}>
              <Input
                placeholder={t('profile:businessInfo.email')}
                type="email"
                fullWidth
              />
            </FormItem>
          </div>

          {/* Số điện thoại */}
          <div>
            <FormItem name="businessPhone" label={t('profile:businessInfo.phone')}>
              <Input
                placeholder={t('profile:businessInfo.phone')}
                type="tel"
                fullWidth
              />
            </FormItem>
          </div>

          {/* Buttons - Luôn hiển thị để test */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.cancel')}
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.save')}
            </Button>
          </div>
        </div>
      </Form>
    </ProfileCard>
  );
};

export default BusinessInfoForm;
