import React from 'react';
import { useTranslation } from 'react-i18next';
import MBBankAccountForm from '../components/forms/MBBankAccountForm';
import { MBBankAccountFormValues } from '../types/banking.types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang liên kết tài khoản ngân hàng MB Bank
 */
const MBBankingPage: React.FC = () => {
  const { t } = useTranslation(['integrations', 'common']);

  // Xử lý submit form
  const handleSubmit = async (values: MBBankAccountFormValues) => {
    try {
      console.log('MB Bank Account Data:', values);

      // TODO: Gọi API để lưu thông tin tài khoản MB Bank
      // await createMBBankAccount(values);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('integrations:banking.mb.saveSuccess', '<PERSON><PERSON> lưu thông tin tài khoản MB Bank thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Save MB account error:', error);
      NotificationUtil.error({
        message: t('integrations:banking.mb.saveError', 'Có lỗi xảy ra khi lưu thông tin tài khoản MB Bank'),
        duration: 5000,
      });
      throw error; // Re-throw để form xử lý
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <MBBankAccountForm onSubmit={handleSubmit} />
    </div>
  );
};

export default MBBankingPage;
