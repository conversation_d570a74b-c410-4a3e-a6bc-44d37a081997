import { apiClient } from '@/shared/api/axios';
import {
  UserDto,
  BusinessInfoDto,
  BankInfoDto,
  TwoFactorAuthDto,
  UserManageNotificationDto,
  UpdatePersonalInfoDto,
  UpdateBankInfoDto,
  UpdateBusinessInfoDto,
  AvatarUploadDto,
  AvatarUploadResponseDto,
  UpdateAvatarDto,
  UpdateNotificationSettingsDto,
  ToggleSmsAuthDto,
  ToggleEmailAuthDto,
  VerifyGoogleAuthDto,
  GoogleAuthSetupDto,
  BankDto,
  BankQueryDto,
  ChangePasswordDto,
} from '../types/user.types';
import { UpdateCoverImageRequest, UserProfile } from '../types/profile.types';

// Không cần thêm '/v1' vì đã được cấu hình trong axios.ts
const API_BASE_URL = '';

/**
 * Lấy thông tin người dùng hiện tại
 * @returns Thông tin người dùng
 */
export const getCurrentUser = async (): Promise<UserDto> => {
  const response = await apiClient.get<UserDto>(`${API_BASE_URL}/users/profile`);
  return response.result;
};

/**
 * Cập nhật thông tin cá nhân
 * @param data Thông tin cá nhân cần cập nhật
 * @returns Thông tin người dùng đã cập nhật
 */
export const updatePersonalInfo = async (data: UpdatePersonalInfoDto): Promise<UserDto> => {
  const response = await apiClient.put<UserDto>(`${API_BASE_URL}/users/profile`, data);
  return response.result;
};

/**
 * Lấy thông tin doanh nghiệp
 * @returns Thông tin doanh nghiệp
 */
export const getBusinessInfo = async (): Promise<BusinessInfoDto | null> => {
  const response = await apiClient.get<BusinessInfoDto | null>(
    `${API_BASE_URL}/user/account/business-info`
  );
  return response.result;
};

/**
 * Cập nhật thông tin doanh nghiệp
 * @param data Thông tin doanh nghiệp cần cập nhật
 * @returns Thông tin doanh nghiệp đã cập nhật
 */
export const updateBusinessInfo = async (data: UpdateBusinessInfoDto): Promise<BusinessInfoDto> => {
  const response = await apiClient.put<BusinessInfoDto>(
    `${API_BASE_URL}/user/account/business-info`,
    data
  );
  return response.result;
};

/**
 * Lấy thông tin ngân hàng
 * @returns Thông tin ngân hàng
 */
export const getBankInfo = async (): Promise<BankInfoDto | null> => {
  const response = await apiClient.get<BankInfoDto | null>(
    `${API_BASE_URL}/user/account/bank-info`
  );
  return response.result;
};

/**
 * Cập nhật thông tin ngân hàng
 * @param data Thông tin ngân hàng cần cập nhật
 * @returns Thông tin ngân hàng đã cập nhật
 */
export const updateBankInfo = async (data: UpdateBankInfoDto): Promise<BankInfoDto> => {
  const response = await apiClient.put<BankInfoDto>(
    `${API_BASE_URL}/user/account/bank-info`,
    data
  );
  return response.result;
};

/**
 * Lấy danh sách ngân hàng
 * @param query Tham số truy vấn
 * @returns Danh sách ngân hàng
 */
export const getBanks = async (query?: BankQueryDto): Promise<BankDto[]> => {
  const response = await apiClient.get<BankDto[]>(`/banks/all`, {
    params: query,
  });
  return response.result;
};

/**
 * Lấy tất cả ngân hàng (không phân trang)
 * @returns Danh sách tất cả ngân hàng
 */
export const getAllBanks = async (): Promise<BankDto[]> => {
  const response = await apiClient.get<BankDto[]>(`${API_BASE_URL}/banks/all`);
  return response.result;
};

/**
 * Tạo URL tạm thời để tải lên avatar
 * @param data Thông tin về loại và kích thước avatar
 * @returns URL tạm thời và thông tin khóa S3
 */
export const createAvatarUploadUrl = async (
  data: AvatarUploadDto
): Promise<AvatarUploadResponseDto> => {
  const response = await apiClient.post<AvatarUploadResponseDto>(
    `${API_BASE_URL}/user/account/avatar/upload-url`,
    data
  );
  return response.result;
};

/**
 * Cập nhật avatar
 * @param data Thông tin avatar cần cập nhật
 * @returns Thông tin avatar đã cập nhật
 */
export const updateAvatar = async (data: UpdateAvatarDto): Promise<{ avatar: string }> => {
  const response = await apiClient.put<{ avatar: string }>(
    `${API_BASE_URL}/user/account/avatar`,
    data
  );
  return response.result;
};

/**
 * Lấy trạng thái xác thực hai yếu tố
 * @returns Trạng thái xác thực hai yếu tố
 */
export const getTwoFactorAuthStatus = async (): Promise<TwoFactorAuthDto> => {
  const response = await apiClient.get<TwoFactorAuthDto>(
    `${API_BASE_URL}/users/two-factor-auth/status`
  );
  console.log('2FA Status:', response.result);
  return response.result;
};

/**
 * Bật/tắt xác thực hai lớp qua SMS
 * @param data Trạng thái bật/tắt
 * @returns Trạng thái xác thực hai yếu tố
 */
export const toggleSmsAuth = async (data: ToggleSmsAuthDto): Promise<TwoFactorAuthDto> => {
  const response = await apiClient.post<TwoFactorAuthDto>(
    `${API_BASE_URL}/users/two-factor-auth/sms`,
    data
  );
  return response.result;
};

/**
 * Bật/tắt xác thực hai lớp qua email
 * @param data Trạng thái bật/tắt
 * @returns Trạng thái xác thực hai yếu tố
 */
export const toggleEmailAuth = async (data: ToggleEmailAuthDto): Promise<TwoFactorAuthDto> => {
  const response = await apiClient.post<TwoFactorAuthDto>(
    `${API_BASE_URL}/users/two-factor-auth/email`,
    data
  );
  return response.result;
};

/**
 * Thiết lập Google Authenticator
 * @returns Thông tin thiết lập Google Authenticator
 */
export const setupGoogleAuth = async (): Promise<GoogleAuthSetupDto> => {
  const response = await apiClient.post<GoogleAuthSetupDto>(
    `${API_BASE_URL}/users/two-factor-auth/google/setup`
  );
  return response.result;
};

/**
 * Xác nhận cài đặt Google Authenticator
 * @param data Mã xác thực từ Google Authenticator
 * @returns Trạng thái xác thực hai yếu tố
 */
export const verifyGoogleAuth = async (data: VerifyGoogleAuthDto): Promise<TwoFactorAuthDto> => {
  const response = await apiClient.post<TwoFactorAuthDto>(
    `${API_BASE_URL}/users/two-factor-auth/google/verify`,
    data
  );
  console.log('Verify Google Auth Response:', response.result);
  return response.result;
};

/**
 * Bật/tắt xác thực hai lớp qua Google Authenticator
 * @param data Trạng thái bật/tắt
 * @returns Trạng thái xác thực hai yếu tố
 */
export const toggleGoogleAuth = async (data: ToggleEmailAuthDto): Promise<TwoFactorAuthDto> => {
  const response = await apiClient.post<TwoFactorAuthDto>(
    `${API_BASE_URL}/users/two-factor-auth/google`,
    data
  );
  return response.result;
};

/**
 * Lấy cài đặt thông báo
 * @returns Cài đặt thông báo
 */
export const getNotificationSettings = async (): Promise<UserManageNotificationDto> => {
  const response = await apiClient.get<UserManageNotificationDto>(
    `${API_BASE_URL}/user/account/notification-settings`
  );
  return response.result;
};

/**
 * Cập nhật cài đặt thông báo
 * @param data Cài đặt thông báo cần cập nhật
 * @returns Cài đặt thông báo đã cập nhật
 */
export const updateNotificationSettings = async (
  data: UpdateNotificationSettingsDto
): Promise<UserManageNotificationDto> => {
  const response = await apiClient.put<UserManageNotificationDto>(
    `${API_BASE_URL}/user/account/notification-settings`,
    data
  );
  return response.result;
};

// Không cần định nghĩa interface vì chúng ta đang sử dụng Record<string, unknown>

/**
 * Lấy số point của người dùng hiện tại
 * @returns Số point hiện tại của người dùng
 */
export const getUserPoints = async (): Promise<number> => {
  try {
    // Không cần chỉ định tokenType, token sẽ được tự động thêm vào request
    const response = await apiClient.get(`${API_BASE_URL}/users/points`);
    console.log('API Response:', JSON.stringify(response)); // Log để debug

    // Kiểm tra cấu trúc dữ liệu từ API
    if (response && response.result) {
      // Trường hợp 1: result là một đối tượng có thuộc tính pointsBalance
      if (typeof response.result === 'object' && response.result !== null) {
        // Sử dụng type assertion để truy cập thuộc tính pointsBalance
        const resultObj = response.result as Record<string, unknown>;
        if ('pointsBalance' in resultObj && resultObj['pointsBalance']) {
          const pointsStr = String(resultObj['pointsBalance']);
          const points = parseInt(pointsStr, 10);
          console.log('Parsed points from object:', points);
          return isNaN(points) ? 0 : points;
        }
      }

      // Trường hợp 2: result là một số
      if (typeof response.result === 'number') {
        console.log('Points as number:', response.result);
        return response.result;
      }

      // Trường hợp 3: result là một chuỗi số
      if (typeof response.result === 'string') {
        const points = parseInt(response.result, 10);
        console.log('Parsed points from string:', points);
        return isNaN(points) ? 0 : points;
      }
    }

    console.log('No valid points data found, returning 0');
    return 0;
  } catch (error) {
    console.error('Error fetching user points:', error);
    return 0;
  }
};

/**
 * Cập nhật ảnh bìa
 */
export const updateCoverImage = async (request: UpdateCoverImageRequest): Promise<UserProfile> => {
  const response = await apiClient.put<UserProfile>(`${API_BASE_URL}/cover-image`, request);
  return response.result;
};

/**
 * Đổi mật khẩu
 * @param data Thông tin mật khẩu cũ và mật khẩu mới
 * @returns Thông báo thành công
 */
export const changePassword = async (data: ChangePasswordDto): Promise<{ message: string }> => {
  const response = await apiClient.post<{ message: string }>(
    `${API_BASE_URL}/users/change-password`,
    data
  );
  return response.result;
};
