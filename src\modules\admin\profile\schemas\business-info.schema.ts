import { z } from 'zod';
import { TFunction } from 'i18next';

/**
 * Schema creator function for business info form with translation support
 */
export const createBusinessInfoSchema = (t: TFunction) =>
  z.object({
    businessName: z.string().min(1, t('validation:required')),
    businessEmail: z.string().email(t('validation:invalidEmail')).min(1, t('validation:required')),
    businessPhone: z.string().min(1, t('validation:required')),
    taxCode: z.string().min(1, t('validation:required')),
    businessAddress: z.string().min(1, t('validation:required')),
    representativeName: z.string().min(1, t('validation:required')),
    representativePosition: z.string().min(1, t('validation:required')),
    businessRegistrationCertificate: z.string().optional(),
  });

/**
 * Type for business info schema
 */
export type BusinessInfoSchema = z.infer<ReturnType<typeof createBusinessInfoSchema>>;

/**
 * Static business info schema (without translation support)
 */
export const businessInfoSchema = z.object({
  companyName: z.string().min(1, 'validation:required'),
  taxCode: z.string().min(1, 'validation:required'),
  representative: z.string().min(1, 'validation:required'),
  position: z.string().min(1, 'validation:required'),
  address: z.string().min(1, 'validation:required'),
  email: z.string().email('validation:invalidEmail').min(1, 'validation:required'),
  phone: z.string().min(1, 'validation:required'),
});
