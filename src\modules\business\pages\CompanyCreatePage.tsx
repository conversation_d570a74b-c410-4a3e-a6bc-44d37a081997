/**
 * Company Create Page
 * Trang tạo công ty mới
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import CompanyCreateForm from '../components/forms/CompanyCreateForm';

/**
 * Company Create Page Component
 */
const CompanyCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation(['business']);

  /**
   * Xử lý khi tạo công ty thành công
   */
  const handleSuccess = (companyId: string) => {
    console.log('Company created successfully with ID:', companyId);
    
    // <PERSON><PERSON> thể navigate đến trang chi tiết công ty hoặc danh sách công ty
    // navigate(`/business/companies/${companyId}`);
    // hoặc quay lại trang trước
    navigate(-1);
  };

  /**
   * Xử lý khi hủy tạo công ty
   */
  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <CompanyCreateForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default CompanyCreatePage;
