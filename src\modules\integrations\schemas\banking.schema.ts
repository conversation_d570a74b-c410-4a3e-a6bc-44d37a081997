import { z } from 'zod';

/**
 * Schema validation cho ACB Bank Account Form
 */
export const acbBankAccountSchema = z.object({
  account_holder_name: z
    .string()
    .min(1, 'Tên chủ tài khoản là bắt buộc')
    .max(100, 'Tên chủ tài khoản không được vượt quá 100 ký tự'),
  
  account_number: z
    .string()
    .min(1, 'Số tài khoản là bắt buộc')
    .max(20, 'Số tài khoản không được vượt quá 20 ký tự')
    .regex(/^[0-9]+$/, 'Số tài khoản chỉ được chứa số'),
  
  phone_number: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .max(20, 'Số điện thoại không được vượt quá 20 ký tự')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),
  
  label: z
    .string()
    .max(100, 'Tên gợi nhớ không được vượt quá 100 ký tự')
    .optional()
    .or(z.literal('')),
});

export type ACBBankAccountFormValues = z.infer<typeof acbBankAccountSchema>;

/**
 * Schema validation cho MB Bank Account Form
 */
export const mbBankAccountSchema = z.object({
  account_holder_name: z
    .string()
    .min(1, 'Tên chủ tài khoản là bắt buộc')
    .max(100, 'Tên chủ tài khoản không được vượt quá 100 ký tự'),

  account_number: z
    .string()
    .min(1, 'Số tài khoản là bắt buộc')
    .max(20, 'Số tài khoản không được vượt quá 20 ký tự')
    .regex(/^[0-9]+$/, 'Số tài khoản chỉ được chứa số'),

  identification_number: z
    .string()
    .min(1, 'Số CMND/CCCD là bắt buộc')
    .max(100, 'Số CMND/CCCD không được vượt quá 100 ký tự'),

  phone_number: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .max(20, 'Số điện thoại không được vượt quá 20 ký tự')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),

  label: z
    .string()
    .max(100, 'Tên gợi nhớ không được vượt quá 100 ký tự')
    .optional()
    .or(z.literal('')),
});

export type MBBankAccountFormValues = z.infer<typeof mbBankAccountSchema>;

/**
 * Schema validation cho OCB Bank Account Form
 */
export const ocbBankAccountSchema = z.object({
  account_holder_name: z
    .string()
    .min(1, 'Tên chủ tài khoản là bắt buộc')
    .max(100, 'Tên chủ tài khoản không được vượt quá 100 ký tự'),

  account_number: z
    .string()
    .min(1, 'Số tài khoản là bắt buộc')
    .max(20, 'Số tài khoản không được vượt quá 20 ký tự')
    .regex(/^[0-9]+$/, 'Số tài khoản chỉ được chứa số'),

  identification_number: z
    .string()
    .min(1, 'Số CMND/CCCD là bắt buộc')
    .max(100, 'Số CMND/CCCD không được vượt quá 100 ký tự'),

  phone_number: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .max(20, 'Số điện thoại không được vượt quá 20 ký tự')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),

  label: z
    .string()
    .max(100, 'Tên gợi nhớ không được vượt quá 100 ký tự')
    .optional()
    .or(z.literal('')),
});

export type OCBBankAccountFormValues = z.infer<typeof ocbBankAccountSchema>;
