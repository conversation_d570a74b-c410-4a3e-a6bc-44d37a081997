import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Icon,
  Typography,
  Toggle,
  Loading,
} from '@/shared/components/common';
import { FormStatus, NotificationOption } from '../types/profile.types';
import { useNotificationSettings, useUpdateNotificationSettings } from '../hooks/useUser';
import { useProfileNotification } from '../contexts/ProfileNotificationContext';
import { useProfileAccordion } from '../hooks/useProfileAccordion';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { UpdateNotificationSettingsDto } from '../types/user.types';
import ProfileCard from './ProfileCard';

/**
 * Component tùy chọn thông báo
 */
const NotificationSettings: React.FC = () => {
  const { t } = useTranslation(['profile']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const [options, setOptions] = useState<NotificationOption[]>([]);
  const { showNotification } = useProfileNotification();
  const { isCardOpen } = useProfileAccordion();

  // Kiểm tra xem card có đang mở không để lazy load
  const isCardOpened = isCardOpen(PROFILE_CARD_IDS.NOTIFICATION_SETTINGS);

  // Sử dụng hooks để lấy và cập nhật dữ liệu - chỉ khi card được mở
  const { data: notificationSettings, isLoading, error } = useNotificationSettings({
    enabled: isCardOpened,
  });
  const updateNotificationSettingsMutation = useUpdateNotificationSettings();

  // Cập nhật options khi có dữ liệu từ API
  useEffect(() => {
    if (notificationSettings) {
      // Chuyển đổi từ UserManageNotificationDto sang NotificationOption[]
      const notificationOptions: NotificationOption[] = [
        {
          id: 'accountSystem',
          name: t('profile:notifications.options.accountSystem.name'),
          description: t('profile:notifications.options.accountSystem.description'),
          enabled: notificationSettings.receiveAccountSystemEmails || false,
        },
        {
          id: 'billing',
          name: t('profile:notifications.options.billing.name'),
          description: t('profile:notifications.options.billing.description'),
          enabled: notificationSettings.receiveBillingEmails || false,
        },
        {
          id: 'newFeature',
          name: t('profile:notifications.options.newFeature.name'),
          description: t('profile:notifications.options.newFeature.description'),
          enabled: notificationSettings.receiveNewFeatureEmails || false,
        },
        {
          id: 'affiliate',
          name: t('profile:notifications.options.affiliate.name'),
          description: t('profile:notifications.options.affiliate.description'),
          enabled: notificationSettings.receiveAffiliateEmails || false,
        },
        {
          id: 'documentation',
          name: t('profile:notifications.options.documentation.name'),
          description: t('profile:notifications.options.documentation.description'),
          enabled: notificationSettings.receiveDocumentationEmails || false,
        },
        {
          id: 'promotional',
          name: t('profile:notifications.options.promotional.name'),
          description: t('profile:notifications.options.promotional.description'),
          enabled: notificationSettings.receivePromotionalEmails || false,
        },
      ];
      setOptions(notificationOptions);
    }
  }, [notificationSettings, t]);

  // Xử lý khi thay đổi trạng thái checkbox
  const handleCheckboxChange = (id: string, checked: boolean) => {
    setOptions(prevOptions =>
      prevOptions.map(option => (option.id === id ? { ...option, enabled: checked } : option))
    );
  };

  // Xử lý khi submit form
  const handleSave = () => {
    setFormStatus(FormStatus.SUBMITTING);
    console.log('[NotificationSettings] Saving notification settings...');

    // Chuyển đổi từ NotificationOption[] sang UpdateNotificationSettingsDto
    const updateData: UpdateNotificationSettingsDto = {
      receiveAccountSystemEmails: options.find(opt => opt.id === 'accountSystem')?.enabled || false,
      receiveBillingEmails: options.find(opt => opt.id === 'billing')?.enabled || false,
      receiveNewFeatureEmails: options.find(opt => opt.id === 'newFeature')?.enabled || false,
      receiveAffiliateEmails: options.find(opt => opt.id === 'affiliate')?.enabled || false,
      receiveDocumentationEmails: options.find(opt => opt.id === 'documentation')?.enabled || false,
      receivePromotionalEmails: options.find(opt => opt.id === 'promotional')?.enabled || false,
    };

    updateNotificationSettingsMutation.mutate(updateData, {
      onSuccess: () => {
        setFormStatus(FormStatus.IDLE);
        console.log('[NotificationSettings] Settings saved successfully, showing notification');
        const notificationId = showNotification('success', t('profile:messages.updateSuccess'));
        console.log('[NotificationSettings] Notification shown with ID:', notificationId);
      },
      onError: error => {
        setFormStatus(FormStatus.IDLE);
        console.log('[NotificationSettings] Error saving settings:', error);
        showNotification('error', t('profile:messages.updateError'));
      },
    });
  };

  // Kiểm tra xem form có đang ở trạng thái submit không
  const isSubmitting = formStatus === FormStatus.SUBMITTING;

  // Kiểm tra xem có sự thay đổi nào không
  const hasChanges = notificationSettings
    ? options.find(opt => opt.id === 'accountSystem')?.enabled !==
        notificationSettings.receiveAccountSystemEmails ||
      options.find(opt => opt.id === 'billing')?.enabled !==
        notificationSettings.receiveBillingEmails ||
      options.find(opt => opt.id === 'newFeature')?.enabled !==
        notificationSettings.receiveNewFeatureEmails ||
      options.find(opt => opt.id === 'affiliate')?.enabled !==
        notificationSettings.receiveAffiliateEmails ||
      options.find(opt => opt.id === 'documentation')?.enabled !==
        notificationSettings.receiveDocumentationEmails ||
      options.find(opt => opt.id === 'promotional')?.enabled !==
        notificationSettings.receivePromotionalEmails
    : false;

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="mail" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:notifications.title')}
      </Typography>
    </div>
  );

  // Nếu card chưa được mở, chỉ hiển thị card rỗng
  if (!isCardOpened) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.NOTIFICATION_SETTINGS} title={cardTitle}>
        <div></div>
      </ProfileCard>
    );
  }

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.NOTIFICATION_SETTINGS} title={cardTitle}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
        </div>
      </ProfileCard>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error || !notificationSettings) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.NOTIFICATION_SETTINGS} title={cardTitle}>
        <div className="text-red-500 py-4">{t('profile:error.loadingNotificationSettings')}</div>
      </ProfileCard>
    );
  }

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.NOTIFICATION_SETTINGS} title={cardTitle}>
      <div className="space-y-4">
        <Typography variant="body1" color="muted">
          {t('profile:notifications.description')}
        </Typography>

        <div className="mt-4 sm:mt-6 space-y-0">
          {options.length > 0 ? (
            options.map(option => (
              <div
                key={option.id}
                className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 border-b border-gray-100 dark:border-gray-800 py-3 sm:py-4"
              >
                <div className="flex-1">
                  <div className="font-medium">
                    {t(`profile:notifications.options.${option.id}.name`)}
                  </div>
                  <Typography variant="caption" color="muted" className="mt-1">
                    {t(`profile:notifications.options.${option.id}.description`)}
                  </Typography>
                </div>
                <div className="flex-shrink-0">
                  <Toggle
                    checked={option.enabled}
                    onChange={(checked: boolean) => handleCheckboxChange(option.id, checked)}
                    id={`notification-${option.id}`}
                  />
                </div>
              </div>
            ))
          ) : (
            <div className="py-4 text-center text-gray-500">
              {t('profile:notifications.noOptions')}
            </div>
          )}
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={!hasChanges || isSubmitting}
            isLoading={isSubmitting}
          >
            {t('profile:buttons.save')}
          </Button>
        </div>
      </div>
    </ProfileCard>
  );
};

export default NotificationSettings;
