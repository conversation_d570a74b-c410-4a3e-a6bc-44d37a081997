import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { addChatNotification } from '@/shared/store/slices/chatSlice';
import { useChatPanel } from '@/shared/contexts/chat-panel';

/**
 * Hook để gửi thông báo đến chat panel
 */
const useChatPanelNotification = () => {
  const dispatch = useDispatch();
  const { isChatPanelOpen, setIsChatPanelOpen } = useChatPanel();

  /**
   * Hiển thị thông báo trong chat panel
   * @param type Loại thông báo: 'success', 'error', 'warning', 'info'
   * @param message Nội dung thông báo
   * @returns ID của thông báo
   */
  const showNotification = useCallback(
    (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
      const id = Date.now().toString();

      // Mở chat panel nếu chưa mở
      if (!isChatPanelOpen) {
        setIsChatPanelOpen(true);
      }

      // Dispatch action để thêm thông báo vào chat panel
      dispatch(
        addChatNotification({
          id,
          type,
          message,
          duration: 5000,
        })
      );

      // Log để debug
      console.log(`[ChatPanelNotification] Added notification: ${type} - ${message} - ID: ${id}`);

      return id;
    },
    [dispatch, isChatPanelOpen, setIsChatPanelOpen]
  );

  return { showNotification };
};

export default useChatPanelNotification;
