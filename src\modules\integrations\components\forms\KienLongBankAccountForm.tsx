import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { KienLongBankAccountFormValues } from '../../types/banking.types';
import { kienLongBankAccountSchema } from '../../schemas/banking.schema';
import { NotificationUtil } from '@/shared/utils/notification';

interface KienLongBankAccountFormProps {
  /**
   * Dữ liệu ban đầu (cho chế độ chỉnh sửa)
   */
  initialData?: KienLongBankAccountFormValues;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: KienLongBankAccountFormValues) => Promise<void>;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;

  /**
   * Trạng thái loading
   */
  loading?: boolean;
}

/**
 * Form liên kết tài khoản ngân hàng <PERSON>ên Long
 */
const KienLongBankAccountForm: React.FC<KienLongBankAccountFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const { t } = useTranslation(['integrations', 'common']);
  const { formRef, setFormErrors } = useFormErrors<KienLongBankAccountFormValues>();

  // State cho form data
  const [formData, setFormData] = useState<KienLongBankAccountFormValues>({
    account_holder_name: initialData?.account_holder_name || '',
    account_number: initialData?.account_number || '',
    identification_number: initialData?.identification_number || '',
    phone_number: initialData?.phone_number || '',
    label: initialData?.label || '',
  });

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof KienLongBankAccountFormValues, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý submit form
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate dữ liệu với Zod schema
      const validatedData = kienLongBankAccountSchema.parse(formData);

      // Gọi callback onSubmit
      await onSubmit(validatedData);
    } catch (error) {
      if (error instanceof Error && 'issues' in error) {
        // Zod validation errors
        const zodError = error as any;
        const fieldErrors: Partial<KienLongBankAccountFormValues> = {};
        
        zodError.issues.forEach((issue: any) => {
          const fieldName = issue.path[0] as keyof KienLongBankAccountFormValues;
          fieldErrors[fieldName] = issue.message;
        });

        setFormErrors(fieldErrors);
      } else {
        console.error('Form submission error:', error);
        NotificationUtil.error({
          message: t('integrations:banking.kienlong.submitError', 'Có lỗi xảy ra khi lưu thông tin tài khoản'),
          duration: 5000,
        });
      }
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Card className="border-0">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <Typography variant="h5" className="font-semibold mb-2">
              {t('integrations:banking.kienlong.title', 'Liên kết tài khoản Kiên Long Bank')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integrations:banking.kienlong.description', 'Nhập thông tin tài khoản ngân hàng Kiên Long để liên kết với hệ thống')}
            </Typography>
          </div>

          {/* Form */}
          <Form ref={formRef} onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* Tên chủ tài khoản */}
              <FormItem
                label={t('integrations:banking.kienlong.accountHolderName', 'Tên chủ tài khoản')}
                name="account_holder_name"
                required
              >
                <Input
                  type="text"
                  value={formData.account_holder_name}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  placeholder={t('integrations:banking.kienlong.accountHolderNamePlaceholder', 'Nhập tên chủ tài khoản Kiên Long Bank')}
                  fullWidth
                />
              </FormItem>

              {/* Số tài khoản */}
              <FormItem
                label={t('integrations:banking.kienlong.accountNumber', 'Số tài khoản')}
                name="account_number"
                required
              >
                <Input
                  type="text"
                  value={formData.account_number}
                  onChange={(e) => handleInputChange('account_number', e.target.value)}
                  placeholder={t('integrations:banking.kienlong.accountNumberPlaceholder', 'Nhập số tài khoản Kiên Long Bank')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Số CMND/CCCD */}
              <FormItem
                label={t('integrations:banking.kienlong.identificationNumber', 'Số CMND/CCCD')}
                name="identification_number"
                required
              >
                <Input
                  type="text"
                  value={formData.identification_number}
                  onChange={(e) => handleInputChange('identification_number', e.target.value)}
                  placeholder={t('integrations:banking.kienlong.identificationNumberPlaceholder', 'Nhập số CMND/CCCD đăng ký tài khoản')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>

              {/* Số điện thoại */}
              <FormItem
                label={t('integrations:banking.kienlong.phoneNumber', 'Số điện thoại')}
                name="phone_number"
                required
              >
                <Input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => handleInputChange('phone_number', e.target.value)}
                  placeholder={t('integrations:banking.kienlong.phoneNumberPlaceholder', 'Nhập số điện thoại đăng ký Kiên Long Bank')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Tên gợi nhớ */}
              <FormItem
                label={t('integrations:banking.kienlong.label', 'Tên gợi nhớ')}
                name="label"
              >
                <Input
                  type="text"
                  value={formData.label}
                  onChange={(e) => handleInputChange('label', e.target.value)}
                  placeholder={t('integrations:banking.kienlong.labelPlaceholder', 'Nhập tên gợi nhớ (tùy chọn)')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  {t('common:cancel', 'Hủy')}
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                loading={loading}
              >
                {initialData 
                  ? t('common:update', 'Cập nhật')
                  : t('common:save', 'Lưu')
                }
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default KienLongBankAccountForm;
