import React from 'react';
import { useTranslation } from 'react-i18next';
import { Avatar, Typography, Banner, Loading, ProgressBar } from '@/shared/components/common';
import { useUpdateCoverImage } from '../hooks/useUser';
import {
  useCurrentUser,
  useCreateAvatarUploadUrl,
  useUpdateAvatar,
  USER_QUERY_KEYS,
} from '../hooks/useUser';
import { useImageProcessor } from '@/shared/hooks/common/useImageProcessor';
import { useFileUpload } from '@/shared/hooks/common';
import { ImageFormatEnum, ImageQualityEnum } from '@/shared/types/image-format.enum';
import profilebg from '@/shared/assets/images/background/profilebg.jpg';
import { useQueryClient } from '@tanstack/react-query';
import { useProfile } from '../hooks/useProfile';
/**
 * Component hiển thị phần header của trang profile
 * <PERSON><PERSON> gồm ảnh bìa, avatar và thông tin cơ bản
 */
const ProfileHeader: React.FC = () => {
  const { t } = useTranslation('profile');
  const { data: user, isLoading } = useCurrentUser();

  const updateCoverMutation = useUpdateCoverImage();
  const { data: profile } = useProfile();
  // Hooks cho việc xử lý và upload avatar
  const {
    processedImage,
    isProcessing: isProcessingImage,
    processImageFile,
    reset: resetImageProcessor,
  } = useImageProcessor({
    defaultFormat: ImageFormatEnum.WEBP,
    defaultQuality: ImageQualityEnum.HIGH,
    maxSizeBytes: 2 * 1024 * 1024, // 2MB
  });

  const { uploadToUrl, isUploading, uploadProgress } = useFileUpload();
  const { mutateAsync: getUploadUrl, isPending: isGettingUrl } = useCreateAvatarUploadUrl();
  const { mutateAsync: updateAvatar, isPending: isUpdatingAvatar } = useUpdateAvatar();
  const queryClient = useQueryClient();

  // Trạng thái loading tổng hợp cho avatar
  const isAvatarLoading = isProcessingImage || isGettingUrl || isUploading || isUpdatingAvatar;

  // Xử lý khi thay đổi avatar
  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Bước 1: Xử lý và nén ảnh
      const processedImageResult = await processImageFile(file);

      if (!processedImageResult) {
        console.error('Lỗi xử lý ảnh');
        return;
      }

      // Bước 2: Lấy URL tạm thời từ API
      const { uploadUrl, avatarKey } = await getUploadUrl({
        imageType: processedImageResult.file.type,
        maxSize: processedImageResult.file.size,
      });

      // Bước 3: Upload file lên cloud
      await uploadToUrl({
        file: processedImageResult.file,
        presignedUrl: uploadUrl,
        onUploadProgress: progress => {
          console.log(`Upload progress: ${progress}%`);
        },
      });

      // Bước 4: Cập nhật avatar với avatarKey
      const result = await updateAvatar({ avatarKey });

      // Cập nhật user data trong cache để hiển thị avatar mới ngay lập tức
      if (result && result.avatar) {
        // Cập nhật cache cho useCurrentUser
        const currentUser = queryClient.getQueryData(USER_QUERY_KEYS.profile);
        if (currentUser) {
          queryClient.setQueryData(USER_QUERY_KEYS.profile, {
            ...currentUser,
            avatar: result.avatar,
          });
        }

        // Cập nhật cache cho useProfile
        const PROFILE_QUERY_KEY = 'userProfile';
        const currentProfile = queryClient.getQueryData([PROFILE_QUERY_KEY]);
        if (currentProfile) {
          queryClient.setQueryData([PROFILE_QUERY_KEY], {
            ...currentProfile,
            avatarUrl: result.avatar.startsWith('http')
              ? result.avatar
              : `/uploads/${result.avatar}`,
          });
        }
      }

      // Reset image processor
      resetImageProcessor();
    } catch (error) {
      console.error('Lỗi cập nhật avatar:', error);
    }

    // Reset input file để có thể chọn lại cùng một file
    e.target.value = '';
  };

  // Xử lý khi click vào nút thay đổi ảnh bìa trong Banner
  const handleChangeCoverClick = async () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.onchange = async e => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        const file = target.files[0];
        if (file) {
          const imageUrl = URL.createObjectURL(file);
          updateCoverMutation.mutate({ coverImageUrl: imageUrl });
        }
      }
    };
    fileInput.click();
  };

  // Nếu đang tải dữ liệu, hiển thị trạng thái loading
  if (isLoading) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <Loading size="lg" />
        <Typography variant="body1" className="mt-4">
          {t('loading')}
        </Typography>
      </div>
    );
  }

  // Lấy URL avatar từ processedImage (preview), profile, user hoặc sử dụng avatar mặc định
  const avatarUrl =
    processedImage?.previewUrl ||
    profile?.avatarUrl ||
    (user?.avatar
      ? user.avatar.startsWith('http')
        ? user.avatar
        : `/uploads/${user.avatar}`
      : '/assets/images/default-avatar.png');

  // Lấy URL ảnh bìa từ profile hoặc sử dụng ảnh mặc định
  const coverImageUrl = profile?.coverImageUrl || profilebg;

  return (
    <div className="w-full">
      {/* Banner thay thế cho Cover Image */}
      <Banner
        backgroundImage={coverImageUrl}
        size="md"
        overlayOpacity={0}
        borderRadius="rounded-t-xl"
        primaryAction={{
          label: t('buttons.changeCover'),
          onClick: handleChangeCoverClick,
        }}
      >
        <div className="h-48 md:h-64 lg:h-80"></div>
      </Banner>

      {/* Avatar và thông tin cơ bản */}
      <div className="relative px-4 sm:px-6 lg:px-8 -mt-20 md:-mt-24 lg:-mt-28 flex flex-col items-center">
        {/* Avatar */}
        <div className="relative">
          <label className="cursor-pointer block" htmlFor="avatar-upload">
            <Avatar
              src={avatarUrl}
              alt={user?.fullName || 'User'}
              size="xl"
              className={`border-4 border-white shadow-lg w-24 h-24 md:w-32 md:h-32 lg:w-36 lg:h-36 transition-opacity hover:opacity-80 ${isAvatarLoading ? 'opacity-70' : ''}`}
            />
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleAvatarChange}
              id="avatar-upload"
              disabled={isAvatarLoading}
            />
          </label>

          {/* Loading indicator */}
          {isAvatarLoading && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-50 rounded-full">
              {isUploading ? (
                <div className="w-3/4">
                  <ProgressBar value={uploadProgress} color="primary" size="sm" />
                  <Typography variant="caption" className="text-white text-center mt-1">
                    {uploadProgress}%
                  </Typography>
                </div>
              ) : (
                <Loading size="sm" className="text-white" />
              )}
            </div>
          )}
        </div>

        {/* Thông tin cơ bản */}
        <div className="mt-4 text-center">
          <Typography variant="h4" className="font-bold text-center">
            {user?.fullName || t('profile.unknownUser')}
          </Typography>
          <Typography variant="body1" color="muted" className="mt-1 text-center">
            {user?.phoneNumber || t('profile.noPhone')}
          </Typography>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
